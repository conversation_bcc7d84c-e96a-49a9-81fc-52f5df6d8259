<template>
  <section class="value-at-risk-weight-selector">
    <value-at-risk-section-header :position="3">
      <span slot="header">
        Select Risk Model
      </span>
    </value-at-risk-section-header>

    <value-at-risk-weight-button :active="revenueAtRisk.revenueAtRiskWeight === 'LOW'" weight="LOW" @select="onClick">
      <span slot="header" class="header">Low Risk</span>
      <span slot="description" class="description">
        With this model we assume the risk of loss is relatively low across the board.
      </span>
    </value-at-risk-weight-button>

    <value-at-risk-weight-button :active="revenueAtRisk.revenueAtRiskWeight === 'MEDIUM'" weight="MEDIUM" @select="onClick">
      <span slot="header" class="header">Balanced Risk</span>
      <span slot="description" class="description">
        This model represents a mid-point between the other modes.
      </span>
    </value-at-risk-weight-button>

    <value-at-risk-weight-button :active="revenueAtRisk.revenueAtRiskWeight === 'HIGH'" weight="HIGH" @select="onClick">
      <span slot="header" class="header">High Risk</span>
      <span slot="description" class="description">
        Here we assign a much higher level of risk for all negative scores.
      </span>
    </value-at-risk-weight-button>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import ValueAtRiskSectionHeader from '@/components/ValueAtRisk/ValueAtRiskSectionHeader';
import ValueAtRiskWeightButton from '@/components/ValueAtRiskWeightSelector/ValueAtRiskWeightButton';
import ValueAtRiskRequest from '@/services/request/ValueAtRiskRequest';

export default {
  name: 'value-at-risk-weight-selector',

  components: {
    ValueAtRiskSectionHeader,
    ValueAtRiskWeightButton,
  },

  data() {
    return {
      weight: null,
    };
  },

  computed: {
    ...mapState('valueAtRisk', ['revenueAtRisk']),
  },

  created() {
    this.weight = this.revenueAtRisk?.revenueAtRiskWeight;
  },

  methods: {
    ...mapActions('valueAtRisk', ['setRevenueAtRisk']),

    async onClick(weight) {
      this.weight = weight;

      const rarToUpdate = {
        ...this.revenueAtRisk,
        revenueAtRiskWeight: weight,
      };

      await this.setRevenueAtRisk({ revenueAtRisk: rarToUpdate });
      await ValueAtRiskRequest.previewVarInfo();
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-weight-selector {
  @include flex("block", "row", "start", "stretch");

  padding: 1.5rem 0;

  .value-at-risk-section-header {
    flex: 0 0 auto;
  }

  .value-at-risk-weight-button {
    margin-right: 2rem;

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
