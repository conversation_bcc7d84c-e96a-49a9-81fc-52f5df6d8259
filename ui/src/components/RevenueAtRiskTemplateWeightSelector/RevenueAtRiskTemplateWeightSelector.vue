<template>
  <section class="revenue-at-risk-template-weight-selector">
    <revenue-at-risk-template-section-header :position="3">
      <span slot="header">
        Select Risk Model
      </span>
    </revenue-at-risk-template-section-header>

    <revenue-at-risk-template-weight-button :active="revenueAtRisk.revenueAtRiskWeight === 'LOW'" weight="LOW" @select="onClick">
      <span slot="header" class="header">Low Risk</span>
      <span slot="description" class="description">
        With this model we assume the risk of loss is relatively low across the board.
      </span>
    </revenue-at-risk-template-weight-button>

    <revenue-at-risk-template-weight-button :active="revenueAtRisk.revenueAtRiskWeight === 'MEDIUM'" weight="MEDIUM" @select="onClick">
      <span slot="header" class="header">Balanced Risk</span>
      <span slot="description" class="description">
        This model represents a mid-point between the other modes.
      </span>
    </revenue-at-risk-template-weight-button>

    <revenue-at-risk-template-weight-button :active="revenueAtRisk.revenueAtRiskWeight === 'HIGH'" weight="HIGH" @select="onClick">
      <span slot="header" class="header">High Risk</span>
      <span slot="description" class="description">
        Here we assign a much higher level of risk for all negative scores.
      </span>
    </revenue-at-risk-template-weight-button>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import RevenueAtRiskTemplateSectionHeader from '@/components/RevenueAtRiskTemplate/RevenueAtRiskTemplateSectionHeader';
import RevenueAtRiskTemplateWeightButton from '@/components/RevenueAtRiskTemplateWeightSelector/RevenueAtRiskTemplateWeightButton';

export default {
  name: 'revenue-at-risk-template-weight-selector',

  components: {
    RevenueAtRiskTemplateSectionHeader,
    RevenueAtRiskTemplateWeightButton,
  },

  created() {
    this.weight = this.revenueAtRisk?.revenueAtRiskWeight;
  },

  computed: {
    ...mapState('revenueAtRiskTemplate', ['revenueAtRisk']),
  },

  methods: {
    ...mapActions('revenueAtRiskTemplate', ['setRevenueAtRisk']),

    onClick(weight) {
      const update = {
        ...this.revenueAtRisk,
        revenueAtRiskWeight: weight,
      };

      this.setRevenueAtRisk({ revenueAtRisk: update });
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.revenue-at-risk-template-weight-selector {
  @include flex("block", "row", "start", "stretch");

  padding: 1.5rem 0;
  width: 100%;

  .revenue-at-risk-template-section-header {
    flex: 0 0 auto;
  }

  .revenue-at-risk-template-weight-button {
    margin-right: 2rem;

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
