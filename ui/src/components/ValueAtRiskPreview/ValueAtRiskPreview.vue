<template>
  <section class="value-at-risk-preview" :class="{ low: isLow, med: isMed, high: isHigh }">
    <section class="header">
      <section class="title">
        <i class="fa-solid fa-calculator icon-calculator" />
        <label>Values Preview</label>
        <value-at-risk-preview-weight-tag />
      </section>

      <section class="total-risk-amount">
        <label>Total Revenue at Risk</label>
        <loading-blocks-overlay v-if="loadingPreview" size="small" />
        <span class="amount" v-else-if="revenueAtRisk.valueAtRiskAmount !== null">{{currency.symbol}}{{valueAtRiskAmount}}</span>
        <span class="amount" v-else>---</span>
      </section>
    </section>

<!--    <section class="description">-->
<!--      <label class="preview">Preview:&nbsp;</label>-->
<!--      <label>Value at Risk Per Theme</label>-->
<!--    </section>-->

<!--    <section v-for="(theme, index) in themes" :key="theme.id" class="theme">-->
<!--      <section class="position">{{ index + 1 }}</section>-->
<!--      <section class="info">-->
<!--        <section class="label">{{ theme.topicLabel }}</section>-->
<!--        <loading-blocks-overlay v-if="loadingPreview || loadingTopics" size="small" />-->
<!--        <section class="value" v-else-if="theme.valueAtRiskAmount">{{currency.symbol}}{{ themeVarAmount(theme) }}</section>-->
<!--        <section class="value" v-else>-&#45;&#45;</section>-->
<!--      </section>-->
<!--    </section>-->
  </section>
</template>

<script>
import { startCase } from 'lodash-es';
import { mapGetters, mapState } from 'vuex';

import CurrencyType from '@/enum/currency-type';
import formatCurrencyNumber from '@/helpers/number-utils';
import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import NetworkKeys from '@/enum/network-keys';
import NetworkStatus from '@/enum/network-status';
import ValueAtRiskPreviewWeightTag from '@/components/ValueAtRiskPreview/ValueAtRiskPreviewWeightTag';

export default {
  name: 'value-at-risk-preview',

  components: {
    LoadingBlocksOverlay,
    ValueAtRiskPreviewWeightTag,
  },

  computed: {
    ...mapGetters('network', ['status']),

    ...mapState('valueAtRisk', ['themes', 'revenueAtRisk']),

    currency() {
      return CurrencyType[this.revenueAtRisk.currency];
    },

    loadingPreview() {
      return this.status(NetworkKeys.VAR_PREVIEW) === NetworkStatus.LOADING;
    },

    loadingTopics() {
      return this.status(NetworkKeys.THEMES_TOPICS) === NetworkStatus.LOADING;
    },

    isLow() {
      return this.weight === 'Low';
    },

    isMed() {
      return this.weight === 'Medium';
    },

    isHigh() {
      return this.weight === 'High';
    },

    valueAtRiskAmount() {
      return formatCurrencyNumber(this.revenueAtRisk.revenueAtRiskAmount);
    },

    weight() {
      return this.revenueAtRisk.revenueAtRiskWeight ? startCase(this.revenueAtRisk?.revenueAtRiskWeight.toLowerCase()) : 'Medium';
    },
  },

  methods: {
    themeVarAmount(theme) {
      return formatCurrencyNumber(theme.revenueAtRiskAmount);
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-preview {
  @include grow;

  height: 100%;
  width: 285px;

  &.low {
    background-color: rgba(54, 180, 34, 0.08);

    .header, .theme {
      border-color: #D6EDD2;
    }
  }

  &.med {
    background-color: #F5F7FF;

    .header, .theme {
      border-color: #DFE5FD;
    }
  }

  &.high {
    background-color: rgba(235, 83, 76, 0.05);

    .header, .theme {
      border-color: #F3DEDE;
    }
  }

  .header {
    //border-bottom: $border-standard;
    font-weight: $font-weight-bold;
    padding: 2rem 1.2rem 1.5rem;

    .title {
      @include flex("block", "row", "start", "center");

      .icon-calculator {
        font-size: $font-size-sm;
        margin-right: 0.4rem;
      }

      label {
        font-size: $font-size-xs;
        margin-right: 0.6rem;
      }
    }

    .total-risk-amount {
      @include flex("block", "column", "start", "start");

      background-color: white;
      border-radius: $border-radius-medium;
      height: 90px;
      margin-top: 1.5rem;
      padding: 1rem;

      label {
        font-size: 11px;
        margin-bottom: 0.5rem;
      }

      .amount {
        @include flex("block", "row", "start", "center");

        font-size: $font-size-xl;
        font-weight: $font-weight-light;
      }
    }
  }

  .description {
    font-size: $font-size-xs;
    padding: 1.5rem 1.2rem 0.5rem;

    .preview {
      font-weight: $font-weight-bold;
    }
  }

  .theme {
    @include flex("block", "row", "start", "stretch");

    border-bottom: $border-standard;
    margin: 0 1.2rem;
    padding: 1rem 0;

    .position {
      @include flex("block", "row", "center", "center");

      background-color: transparent;
      border-radius: 1rem;
      border: 1px solid rgba(19, 28, 41, 0.6);
      font-size: 10.2px;
      font-weight: $font-weight-extra-bold;
      height: 16.5px;
      margin-right: 0.7rem;
      width: 16.5px;
    }

    .info {
      @include flex("block", "column", "start", "start");

      font-size: $font-size-xs;
      height: 36px;

      .label {
        font-weight: 600;
        margin-top: 0.1rem;
        margin-bottom: 0.5rem;
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>
