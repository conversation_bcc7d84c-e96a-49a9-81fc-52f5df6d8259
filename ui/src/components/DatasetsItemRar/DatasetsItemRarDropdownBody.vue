<template>
  <section class="datasets-item-rar-dropdown-body">
    <div class="workspace-calculations">
      <span class="description">Workspace Calculations</span>
      <div class="header">
        <span class="header-text">Types</span>
        <span class="header-text">Created By</span>
      </div>
      <div class="list">
        <div v-for="(item) in templates" :key="item.id" class="item" @click.stop="onClickItem(item)">
          <base-radio-with-tick-mark :value="isSelected(item)" />
          <span class="name" :class="{underline: isSelected(item)}">{{item.name}}</span>
          <div class="default" :class="{show: item.defaultTemplate}">
            <span class="default-tag">Default</span>
          </div>
          <span class="created-by">{{item.user.firstName}} {{item.user.lastName}}</span>
        </div>
      </div>
    </div>
<!--    <div class="deleted-calculations">-->
<!--      <span class="description">Deleted Calculations</span>-->
<!--      <div class="item">-->
<!--        <base-radio-with-tick-mark :value="true" />-->
<!--        <span class="name">Standard Revenue 2025</span>-->
<!--        <div class="deleted">-->
<!--          <span class="deleted-tag">Deleted</span>-->
<!--        </div>-->
<!--        <span class="created-by">Admin Name</span>-->
<!--      </div>-->
<!--    </div>-->
    <div class="dataset-calculations">
      <span class="description">Dataset Calculations</span>
      <div class="btn-add-custom" @click.stop="onClickAddCustom" v-if="!localDataset.datasetRevenueAtRisk || localDataset.datasetRevenueAtRisk.templateId">
        <span>Add Custom</span>
        <i class="fa-sharps fa-solid fa-plus"></i>
      </div>
      <div class="item" v-else-if="localDataset.datasetRevenueAtRisk" @click.stop="onClickExistingRar">
        <base-radio-with-tick-mark :value="true" />
        <span class="name">{{localDataset.datasetRevenueAtRisk.name}}</span>
        <div></div>
        <span class="created-by">{{localDataset.datasetRevenueAtRisk.user.firstName}} {{localDataset.datasetRevenueAtRisk.user.lastName}}</span>
      </div>
    </div>
  </section>
</template>

<script>
import { mapState } from 'vuex';

import BaseRadioWithTickMark from '@/components/Base/BaseRadioWithTickMark';
import IconDelete from '@/components/Icons/IconDelete';
import Route from '@/enum/route';

export default {
  name: 'datasets-item-rar-dropdown-body',

  components: {
    BaseRadioWithTickMark,
    IconDelete,
  },

  props: {
    localDataset: {
      type: Object,
      required: true,
    },
  },

  computed: {
    ...mapState('revenueAtRiskTemplate', ['templates']),

    selectedTemplate() {
      if (this.localDataset.datasetRevenueAtRisk && this.localDataset.datasetRevenueAtRisk.templateId) {
        return this.templates.find((template) => template.id === this.localDataset.datasetRevenueAtRisk.templateId);
      }
      return null;
    },
  },

  methods: {
    isSelected(item) {
      return this.selectedTemplate && item.id === this.selectedTemplate.id;
    },

    onClickAddCustom() {
      this.$router.push({
        name: Route.REVENUE_AT_RISK,
        query: {
          id: this.localDataset.id,
          isCreateNew: true,
        },
      });
    },

    onClickItem(item) {
      this.$emit('selectTemplate', item);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/variables";
@import "src/styles/mixins";

.datasets-item-rar-dropdown-body {
  @include flex("block", "column", "center", "start");

  border-bottom: $border-standard;
  padding: 1rem;
  gap: 1rem;

  .workspace-calculations {
    @include flex("block", "column", "center", "start");

    width: 100%;

    .description {
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      margin-bottom: 1rem;
    }

    .header {
      display: grid;
      grid-gap: 1rem;
      grid-template-columns: 230px 1fr;

      .header-text {
        color: #1F2734;
        font-size: $font-size-xxs;
        font-weight: $font-weight-black;
        opacity: 0.4;
        text-transform: uppercase;
      }
    }

    .list {
      width: 100%;

      .item {
        align-items: center;
        border-radius: 2px;
        cursor: pointer;
        display: grid;
        grid-gap: 2px;
        grid-template-columns: 16px 152px 70px 90px 20px;
        padding: 0.5rem 0;
        width: 100%;

        &:hover {
          background-color: rgba(199, 205, 255, 0.3);
        }

        .name {
          @include truncate;

          color: #494F5A;
          font-size: 11px;
          opacity: 0.9;

          &.underline {
            text-decoration: underline;
          }
        }

        .default {
          @include flex("block", "row", "start", "center");

          visibility: hidden;

          .default-tag {
            background-color: rgba(156, 156, 156, 0.2);
            border-radius: 2px;
            border: 1px solid #D5D5D5;
            color: #3B3B3B;
            font-size: $font-size-xxs;
            font-weight: $font-weight-bold;
            padding: 2px;
            text-transform: uppercase;
          }

          &.show {
            visibility: visible;
          }
        }

        .created-by {
          @include truncate;

          font-size: 11px;
          opacity: 0.5;
        }
      }
    }
  }

  .deleted-calculations {
    @include flex("block", "column", "center", "start");

    width: 100%;

    .description {
      color: #FF1010;
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      margin-bottom: 1rem;
    }

    .item {
      align-items: center;
      display: grid;
      grid-gap: 2px;
      grid-template-columns: 16px 152px 70px 90px 20px;
      padding: 0.5rem 0;
      width: 100%;

      .name {
        @include truncate;

        color: #494F5A;
        font-size: 11px;
        opacity: 0.9;
        text-decoration: underline;
      }

      .deleted {
        @include flex("block", "row", "start", "center");

        .deleted-tag {
          background-color: #FFE6E6;
          border-radius: 2px;
          border: 1px solid #FF1010;
          color: #FF1010;
          font-size: $font-size-xxs;
          font-weight: $font-weight-bold;
          padding: 2px;
          text-transform: uppercase;
        }
      }

      .created-by {
        @include truncate;

        font-size: 11px;
        opacity: 0.5;
      }

      .icon-delete {
        height: 22px;
        width: 22px;
      }
    }
  }

  .dataset-calculations {
    @include flex("block", "column", "center", "start");

    width: 100%;

    .description {
      font-size: $font-size-sm;
      font-weight: $font-weight-bold;
      margin-bottom: 1rem;
    }

    .btn-add-custom {
      @include flex("block", "row", "center", "center");

      border-radius: 2px;
      border: 1px solid;
      color: #765FDE;
      cursor: pointer;
      font-size: 11px;
      font-weight: $font-weight-black;
      padding: 6px;

      &:hover {
        color: rgb(123, 47, 248, 1.4);
      }
    }

    .item {
      align-items: center;
      border-radius: 2px;
      cursor: pointer;
      display: grid;
      grid-gap: 2px;
      grid-template-columns: 16px 152px 70px 90px 20px;
      padding: 0.5rem 0;
      width: 100%;

      &:hover {
        background-color: rgba(199, 205, 255, 0.3);
      }

      .name {
        @include truncate;

        color: #494F5A;
        font-size: 11px;
        opacity: 0.9;
        text-decoration: underline;
      }

      .created-by {
        @include truncate;

        font-size: 11px;
        opacity: 0.5;
      }
    }
  }
}
</style>
