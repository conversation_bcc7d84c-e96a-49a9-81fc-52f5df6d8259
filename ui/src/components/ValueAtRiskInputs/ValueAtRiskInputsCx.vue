<template>
  <section class="value-at-risk-inputs-cx">
    <label class="label">Average Customer Spend (Per year)</label>
    <value-at-risk-inputs-number v-model.number="localCustomerSpendAvgAnnual"
      :has-error="revenueAtRiskError.customerSpendAvgAnnual"
      :icon="currency.fontawesomeIcon"
      @input="onInputCustomerSpendAvgAnnual"
      class="input"
      error-message="Please enter a value for 'Average Customer Spend' to continue."
    />

    <label class="label">Additional Costs (e.g. XYZ costs)</label>
    <value-at-risk-inputs-number v-model.number="localCustomerAdditionalCost"
      class="input"
      :icon="currency.fontawesomeIcon"
      @input="onInputAdditionalCost"
    />

    <label class="label">How long do your customers stay? (In years)</label>
    <value-at-risk-inputs-number v-model.number="localTotalYear"
      class="input"
      icon-svg="calendar"
      @input="onInputTotalYear"
      :has-error="revenueAtRiskError.totalYear"
      error-message="Please enter a value for 'How long do your customers stay?' to continue."
    />

    <section class="scale-people" @click="onToggleSelection">
      <base-checkbox-solid :value="localScaleToTotalPeople" />
      <label class="label">Scale the calculation to your total number of customers?</label>
    </section>

    <section class="number-of-people" v-if="localScaleToTotalPeople" >
      <label class="label">Total Number of Customers</label>
      <value-at-risk-inputs-number v-model.number="localNumberOfCustomers"
        icon="fa-regular fa-users"
        @input="onInputNumberOfCustomers"
        :has-error="revenueAtRiskError.numberOfCustomers"
        error-message="Please enter a value for 'Total Number of Customers' to continue."
      />
    </section>
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import BaseCheckboxSolid from '@/components/Base/BaseCheckboxSolid';
import CurrencyType from '@/enum/currency-type';
import ValueAtRiskInputsNumber from '@/components/ValueAtRiskInputs/ValueAtRiskInputsNumber';
import ValueAtRiskRequest from '@/services/request/ValueAtRiskRequest';

export default {
  name: 'value-at-risk-inputs-cx',

  components: {
    BaseCheckboxSolid,
    ValueAtRiskInputsNumber,
  },

  data() {
    return {
      localCustomerAdditionalCost: 0,
      localCustomerSpendAvgAnnual: 0,
      localNumberOfCustomers: 1,
      localScaleToTotalPeople: false,
      localTotalYear: 1,
      timeout: null,
      error: {
        customerSpendAvgAnnual: false,
        numberOfCustomers: false,
        totalYear: false,
      },
    };
  },

  computed: {
    ...mapState('valueAtRisk', [
      'clear',
      'revenueAtRisk',
      'revenueAtRiskError',
    ]),

    currency() {
      return CurrencyType[this.revenueAtRisk.currency];
    },
  },

  watch: {
    clear() {
      if (this.clear) {
        this.localCustomerAdditionalCost = 0;
        this.localCustomerSpendAvgAnnual = 0;
        this.localNumberOfCustomers = 1;
        this.localScaleToTotalPeople = false;
        this.localTotalYear = 1;
        this.onUpdateModel();
        this.setClear({ clear: false });
      }
    },
  },

  mounted() {
    this.localCustomerAdditionalCost = this.revenueAtRisk.customerAdditionalCost;
    this.localCustomerSpendAvgAnnual = this.revenueAtRisk.customerSpendAvgAnnual;
    this.localNumberOfCustomers = this.revenueAtRisk.numberOfCustomers;
    this.localScaleToTotalPeople = this.revenueAtRisk.scaleToTotalPeople;
    this.localTotalYear = this.revenueAtRisk.totalYear;
    this.setRevenueAtRiskError({ error: this.error });
  },

  methods: {
    ...mapActions('valueAtRisk', [
      'setClear',
      'setRevenueAtRisk',
      'setRevenueAtRiskError',
    ]),

    debounceUpdateModel() {
      clearTimeout(this.timeout);

      this.timeout = setTimeout(() => {
        this.onUpdateModel();
      }, 1000);
    },

    async onUpdateModel() {
      const newRar = {
        ...this.revenueAtRisk,
        customerAdditionalCost: this.localCustomerAdditionalCost,
        customerSpendAvgAnnual: this.localCustomerSpendAvgAnnual,
        numberOfCustomers: this.localNumberOfCustomers,
        scaleToTotalPeople: this.localScaleToTotalPeople,
        totalYear: this.localTotalYear,
      };

      this.setRevenueAtRisk({ revenueAtRisk: newRar });
      this.setRevenueAtRiskError({ error: this.error });

      await ValueAtRiskRequest.previewVarInfo();
    },

    onInputAdditionalCost(value) {
      this.localCustomerAdditionalCost = Number(value);
      this.debounceUpdateModel();
    },

    onInputCustomerSpendAvgAnnual(value) {
      this.localCustomerSpendAvgAnnual = Number(value);
      this.error.customerSpendAvgAnnual = !this.localCustomerSpendAvgAnnual;

      this.debounceUpdateModel();
    },

    onInputNumberOfCustomers(value) {
      this.localNumberOfCustomers = Number(value);
      this.error.numberOfCustomers = !this.localNumberOfCustomers;
      this.debounceUpdateModel();
    },

    onInputTotalYear(value) {
      this.localTotalYear = Number(value);
      this.error.totalYear = !this.localTotalYear;
      this.debounceUpdateModel();
    },

    onToggleSelection() {
      this.localScaleToTotalPeople = !this.localScaleToTotalPeople;
      this.onUpdateModel();
    },
  },

};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-inputs-cx {
  @include flex("block", "column", "start", "stretch");

  margin-right: 2rem;

  .label {
    font-size: $font-size-xs;
    margin-bottom: 0.5rem;
  }

  .input {
    margin-bottom: 1rem;
  }

  .scale-people {
    @include flex("block", "row", "start", "center");

    margin-bottom: 1rem;

    .base-checkbox-solid {
      margin-right: 0.5rem;
    }

    .label {
      cursor: pointer;
      margin-bottom: 0;
    }
  }

  .number-of-people {
    @include flex("block", "row", "start", "start");

    background-color: #F2F4F8;
    border-radius: $border-radius-medium;
    padding: 1.5rem;

    .label {
      margin-bottom: 0;
      margin-right: 1rem;
    }
  }
}
</style>
