<template>
  <section class="value-at-risk-view">
    <section class="loading" v-if="loading">
      <loading-blocks-overlay />
    </section>
    <section v-if="!loading" class="left">
      <value-at-risk-header />
      <value-at-risk-calculator />
      <value-at-risk-footer />
    </section>
    <value-at-risk-preview v-if="!loading" class="right" />
  </section>
</template>

<script>
import { mapActions, mapState } from 'vuex';

import LoadingBlocksOverlay from '@/components/LoadingBlocksOverlay';
import Route from '@/enum/route';
import UpdateTitle from '@/components/Mixins/UpdateTitle';
import ValueAtRiskCalculator from '@/components/ValueAtRisk/ValueAtRiskCalculator';
import ValueAtRiskFooter from '@/components/ValueAtRiskFooter/ValueAtRiskFooter';
import ValueAtRiskHeader from '@/components/ValueAtRiskHeader/ValueAtRiskHeader';
import ValueAtRiskPreview from '@/components/ValueAtRiskPreview/ValueAtRiskPreview';
import ValueAtRiskRequest from '@/services/request/ValueAtRiskRequest';

export default {
  name: 'value-at-risk-view',

  components: {
    LoadingBlocksOverlay,
    ValueAtRiskCalculator,
    ValueAtRiskFooter,
    ValueAtRiskHeader,
    ValueAtRiskPreview,
  },

  mixins: [UpdateTitle],

  data() {
    return {
      loading: true,
    };
  },

  computed: {
    ...mapState('valueAtRisk', ['datasetId']),

    isCreateNew() {
      return this.$router.currentRoute.query?.isCreateNew === 'true';
    },
  },

  async mounted() {
    if (this.$router.currentRoute.query?.id != null) {
      this.setDatasetId({ datasetId: Number(this.$router.currentRoute.query?.id) });
    } else {
      this.$route.push({ name: Route.DATASETS });
    }
    // eslint-disable-next-line no-console
    console.log('isCreateNew', this.isCreateNew);
    await ValueAtRiskRequest.getVarInfo(this.isCreateNew);
    this.loading = false;
  },

  methods: {
    ...mapActions('valueAtRisk', ['setDatasetId']),
  },
};
</script>

<style lang="scss" scoped>
@import 'src/styles/variables';
@import 'src/styles/mixins';

.value-at-risk-view {
  @include flex("block", "row", "start", "start");
  @include stretch;

  background-color: white;
  font-family: Inter, serif;
  height: calc(100vh - #{$header-height});
  max-height: calc(100vh - #{$header-height});
  min-width: 100%;
  width: 100%;

  .loading {
    @include flex("block", "row", "center", "center");

    height: 100%;
    width: 100%;
  }

  .left {
    @include scrollbar-thin;

    border-right: $border-standard;
    height: 100%;
    overflow-y: auto;
    width: 100%;
  }

  .right {
    @include scrollbar-thin;

    height: 100%;
    overflow-y: auto;
  }
}
</style>
