import baseApi from './BaseApi';
import NK from '@/enum/network-keys';

const api = {
  apply: (datasetId) => `/datasets/${datasetId}/revenue-at-risk/apply-template`,
  preview: (datasetId) => `/datasets/${datasetId}/revenue-at-risk/preview`,
  retrieve: (datasetId) => `/datasets/${datasetId}/revenue-at-risk`,
  update: (datasetId) => `/datasets/${datasetId}/revenue-at-risk`,
};

export default {
  ...baseApi,

  async applyRarTemplate(datasetId, templateId) {
    const { error, response } = await this.put(
      NK.VAR_APPLY_TEMPLATE,
      api.apply(datasetId),
      { templateId },
      null,
    );

    if (response?.data !== '') {
      return response.data;
    }

    return error;
  },

  async getVarInfo(datasetId) {
    const { response } = await this.get(NK.VAR, api.retrieve(datasetId));

    if (response?.data !== '') {
      return response.data.revenueAtRisk;
    }

    return {};
  },

  async previewVarInfo(datasetId, revenueAtRisk) {
    const { error, response } = await this.post(
      NK.VAR_PREVIEW,
      api.preview(datasetId),
      { revenueAtRisk },
      null,
    );

    if (response?.data !== '') {
      return response.data.revenueAtRisk;
    }

    return error;
  },

  async updateRevenueAtRisk(datasetId, revenueAtRisk) {
    const { error, response } = await this.put(
      NK.VAR_UPDATE,
      api.update(datasetId),
      { revenueAtRisk },
      null,
    );

    if (response?.data !== '') {
      return response.data.revenueAtRisk;
    }

    return error;
  },
};
