import { mapActions, mapState } from '@/services/request/BaseRequest';

import ThemeApi from '@/services/api/ThemeApi';
import ValueAtRiskApi from '@/services/api/ValueAtRiskApi';

const valueAtRiskRequest = {
  async applyRarTemplate(templateId) {
    await ValueAtRiskApi.applyRarTemplate(this.datasetId, templateId);
  },

  async getVarInfo() {
    const revenueAtRisk = await ValueAtRiskApi.getVarInfo(this.datasetId);
    const themes = await ThemeApi.fetchTopics(this.datasetId);

    // set default to top 3 themes
    if (revenueAtRisk.selectedThemeIds.length === 0) {
      revenueAtRisk.selectedThemeIds = themes.slice(0, 3).map(t => t.id);
    }

    const selectedThemes = themes.filter(theme => revenueAtRisk.selectedThemeIds.includes(theme.id));

    this.setThemes({ themes: selectedThemes });
    this.setRevenueAtRisk({ revenueAtRisk });
  },

  async previewVarInfo() {
    const revenueAtRisk = await ValueAtRiskApi.previewVarInfo(this.datasetId, this.revenueAtRisk);
    const themes = await ThemeApi.fetchTopics(this.datasetId);

    const selectedThemes = themes.filter(theme => revenueAtRisk.selectedThemeIds.includes(theme.id));
    // Sort the selected themes to match the order of revenueAtRisk.selectedThemeIds
    selectedThemes.sort((a, b) => {
      return revenueAtRisk.selectedThemeIds.indexOf(a.id) - revenueAtRisk.selectedThemeIds.indexOf(b.id);
    });

    selectedThemes.forEach(theme => {
      theme.valueAtRiskAmount = theme.valueAtRiskAmountPreview;
    });

    this.setThemes({ themes: selectedThemes });
    this.setRevenueAtRisk({ revenueAtRisk });
  },

  async setRevenueAtRisk() {
    const revenueAtRisk = await ValueAtRiskApi.setRevenueAtRisk(this.datasetId, this.revenueAtRisk);
    await this.setRevenueAtRisk({ revenueAtRisk });
  },
};

mapActions(valueAtRiskRequest, 'valueAtRisk', ['setRevenueAtRisk']);

mapActions(valueAtRiskRequest, 'valueAtRisk', ['setThemes']);

mapState(valueAtRiskRequest, 'valueAtRisk', ['datasetId', 'revenueAtRisk']);

export default valueAtRiskRequest;
