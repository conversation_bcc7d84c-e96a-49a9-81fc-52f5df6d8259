import State from './state';
import types from './types';

const a = types.actions;
// const g = types.getters;
const m = types.mutations;

export default {
  [a.reset]:
  function reset({ commit }) {
    commit(m.reset, { newState: new State() });
  },

  [a.setClear]:
  function setClear({ commit }, { clear }) {
    commit(m.setClear, { clear });
  },

  [a.setDatasetId]:
  function setDatasetId({ commit }, { datasetId }) {
    commit(m.setDatasetId, { datasetId });
  },

  [a.setPreviousRoute]:
  function setPreviousRoute({ commit }, { route }) {
    commit(m.setPreviousRoute, { route });
  },

  [a.setThemes]:
  function setThemes({ commit }, { themes }) {
    commit(m.setThemes, { themes });
  },

  [a.setRevenueAtRisk]:
  function setRevenueAtRisk({ commit }, { revenueAtRisk }) {
    commit(m.setRevenueAtRisk, { revenueAtRisk });
  },

  [a.setRevenueAtRiskError]:
  function setRevenueAtRiskError({ commit }, { error }) {
    commit(m.setRevenueAtRiskError, { error });
  },
};
