import CurrencyType from '@/enum/currency-type';
import Route from '@/enum/route';

export default class State {
  constructor() {
    Object.assign(this, {
      clear: false,
      datasetId: null,
      previousRoute: Route.DATASETS, // DATASET or STORYTELLER
      themes: [],
      revenueAtRisk: {
        // cx fields
        customerAdditionalCost: 0,
        customerSpendAvgAnnual: 0,
        numberOfCustomers: 0,
        totalYear: 1,
        // ex fields
        employeeAdditionalCost: 0,
        employeeSalary: 0,
        numberOfEmployees: 0,
        // general
        currency: CurrencyType.USD.abbreviation,
        scaleToTotalPeople: false,
        selectedThemeIds: [],
        revenueAtRiskAmount: 0,
        revenueAtRiskType: 'CUSTOMER',
        revenueAtRiskWeight: 'MEDIUM',
      },
      revenueAtRiskError: {
        avgEmployeeSalary: false,
        numberOfEmployees: false,

        customerSpendAvgAnnual: false,
        numberOfCustomers: false,
        totalYear: false,
      },
    });
  }
}
