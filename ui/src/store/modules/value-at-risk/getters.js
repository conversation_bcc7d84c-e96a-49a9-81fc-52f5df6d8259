import types from './types';

const g = types.getters;

export default {
  [g.validateVarInfo]: state => {
    const error = {
      avgEmployeeSalary: state.revenueAtRisk.revenueAtRiskType === 'EMPLOYEE' && !state.revenueAtRisk.employeeSalary,
      numberOfEmployees: state.revenueAtRisk.revenueAtRiskType === 'EMPLOYEE' && !state.revenueAtRisk.numberOfEmployees,
      customerSpendAvgAnnual: state.revenueAtRisk.revenueAtRiskType === 'CUSTOMER' && !state.revenueAtRisk.customerSpendAvgAnnual,
      numberOfCustomers: state.revenueAtRisk.revenueAtRiskType === 'CUSTOMER' && !state.revenueAtRisk.numberOfCustomers,
      totalYear: state.revenueAtRisk.revenueAtRiskType === 'CUSTOMER' && !state.revenueAtRisk.totalYear,
    };
    return error;
  },
};
