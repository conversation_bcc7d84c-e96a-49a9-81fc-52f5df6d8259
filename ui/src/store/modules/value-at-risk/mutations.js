import types from './types';

const m = types.mutations;

export default {
  [m.reset]:
  function reset(state, { newState }) {
    Object.assign(state, newState);
  },

  [m.setClear]:
  function setClear(state, { clear }) {
    state.clear = clear;
  },

  [m.setDatasetId]:
  function setDatasetId(state, { datasetId }) {
    state.datasetId = datasetId;
  },

  [m.setPreviousRoute]:
  function setPreviousRoute(state, { route }) {
    state.previousRoute = route;
  },

  [m.setThemes]:
  function setThemes(state, { themes }) {
    state.themes = themes;
  },

  [m.setRevenueAtRisk]:
  function setRevenueAtRisk(state, { revenueAtRisk }) {
    state.revenueAtRisk = revenueAtRisk;
  },

  [m.setRevenueAtRiskError]:
  function setRevenueAtRiskError(state, { error }) {
    state.revenueAtRiskError = error;
  },
};
