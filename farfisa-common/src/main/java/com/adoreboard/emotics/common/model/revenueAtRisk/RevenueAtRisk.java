package com.adoreboard.emotics.common.model.revenueAtRisk;

import com.adoreboard.emotics.common.enums.RevenueAtRiskType;
import com.adoreboard.emotics.common.enums.RevenueAtRiskWeight;
import com.adoreboard.emotics.common.util.NumberUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@SuperBuilder
public class RevenueAtRisk extends RevenueAtRiskTemplateInfo {
    private BigDecimal revenueAtRiskAmount;
    private List<Integer> selectedThemeIds = new ArrayList<>();

    public RevenueAtRisk(RevenueAtRiskType revenueAtRiskType, RevenueAtRiskWeight revenueAtRiskWeight) {
        super(revenueAtRiskType, revenueAtRiskWeight);
    }

    public BigDecimal getRevenueAtRiskAmount() {
        return revenueAtRiskAmount != null ? revenueAtRiskAmount.setScale(2, RoundingMode.HALF_UP) : null;
    }

    @JsonIgnoreProperties
    public String getRevenueAtRiskAmountString() {
        return getRevenueAtRiskAmount() != null
                && BigDecimal.ZERO.compareTo(getRevenueAtRiskAmount()) < 0
                ? NumberUtils.formatCurrencyNumber(this.getRevenueAtRiskAmount()) : "0.00";
    }
}
