package com.adoreboard.emotics.common.model.revenueAtRisk;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatasetRevenueAtRisk {
    private Integer id;
    private Integer datasetId;
    private String name;
    private Integer templateId;
    private int createdBy;
    private RevenueAtRisk revenueAtRisk;
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();
    private LocalDateTime updatedAt;
}
