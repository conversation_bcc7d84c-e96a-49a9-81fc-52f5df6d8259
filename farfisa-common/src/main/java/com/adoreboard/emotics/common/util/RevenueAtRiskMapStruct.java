package com.adoreboard.emotics.common.util;

import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplateInfo;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRisk;

/**
 * <pre>>
 * Utility class for mapping between RevenueAtRiskTemplateInfo and ValueAtRiskInfo.
 *
 * Business Context:
 * This mapper addresses a critical architectural challenge in the revenue-at-risk system:
 *
 * - RevenueAtRiskTemplateInfo: Contains the "blueprint" parameters for risk calculations
 *   (customer spend, employee costs, risk weights, etc.) - stored in workspace templates
 *
 * - ValueAtRiskInfo: Extends the template info but adds calculated results
 *   (actual risk amounts, selected themes) - used in dataset-specific configurations
 *
 * The Problem:
 * While ValueAtRiskInfo extends RevenueAtRiskTemplateInfo, you cannot directly cast
 * a parent instance to a child instance in Java. This mapper provides safe conversion.
 *
 * Usage Patterns:
 * 1. Template → Dataset: When applying workspace templates to new datasets
 * 2. Dataset → Template: When creating new templates from existing configurations
 * 3. API Responses: Ensuring consistent data structures across different contexts
 *
 * This provides a common way to convert template info to value-at-risk info across the codebase.
 * </pre>
 */
public final class RevenueAtRiskMapStruct {

    private RevenueAtRiskMapStruct() {
        // Utility class - prevent instantiation
    }

    /**
     * Creates a ValueAtRiskInfo instance from a RevenueAtRiskTemplateInfo.
     *
     * Business Logic:
     * This conversion is essential when applying workspace templates to datasets:
     *
     * 1. Workspace templates define risk calculation parameters (RevenueAtRiskTemplateInfo)
     * 2. Dataset configurations need calculated results (ValueAtRiskInfo)
     * 3. This method bridges the gap by copying template parameters to a structure
     *    that can hold both parameters and calculated results
     *
     * Data Flow:
     * - Copies all risk parameters (customer/employee costs, weights, currency)
     * - Initializes ValueAtRiskInfo-specific fields with defaults:
     *   * valueAtRiskAmount = null (will be calculated later)
     *   * selectedThemeIds = empty list (will be populated based on analysis)
     *
     * Use Cases:
     * - New dataset creation: Apply workspace default template
     * - Template cloning: Create dataset-specific copy of workspace template
     * - Data migration: Convert legacy template data to new format
     *
     * @param templateInfo The template info to convert (workspace template parameters)
     * @return A new ValueAtRiskInfo instance with the same parameters, ready for calculation
     */
    public static RevenueAtRisk toRevenueAtRisk(RevenueAtRiskTemplateInfo templateInfo) {
        if (templateInfo == null) {
            return null;
        }

        return RevenueAtRisk.builder()
                .customerSpendAvgAnnual(templateInfo.getCustomerSpendAvgAnnual())
                .customerAdditionalCost(templateInfo.getCustomerAdditionalCost())
                .numberOfCustomers(templateInfo.getNumberOfCustomers())
                .totalYear(templateInfo.getTotalYear())
                .employeeSalary(templateInfo.getEmployeeSalary())
                .employeeAdditionalCost(templateInfo.getEmployeeAdditionalCost())
                .numberOfEmployees(templateInfo.getNumberOfEmployees())
                .scaleToTotalPeople(templateInfo.getScaleToTotalPeople())
                .revenueAtRiskType(templateInfo.getRevenueAtRiskType())
                .revenueAtRiskWeight(templateInfo.getRevenueAtRiskWeight())
                .currency(templateInfo.getCurrency())
                // ValueAtRiskInfo specific properties will be initialized with defaults
                // valueAtRiskAmount will be null initially
                // selectedThemeIds will be empty list by default
                .build();
    }

    /**
     * Creates a RevenueAtRiskTemplateInfo instance from a ValueAtRiskInfo.
     *
     * Business Logic:
     * This reverse conversion is useful when creating new workspace templates from
     * successful dataset configurations:
     *
     * 1. User creates and tunes a dataset-specific RAR configuration
     * 2. Configuration proves effective and user wants to make it a workspace template
     * 3. This method extracts just the template parameters, discarding calculated results
     * 4. Result can be saved as a new workspace template for future datasets
     *
     * Data Filtering:
     * - Copies all risk parameters (the "blueprint" for calculations)
     * - Excludes calculated results (valueAtRiskAmount, selectedThemeIds)
     * - Preserves business logic parameters that define how calculations should work
     *
     * Use Cases:
     * - Template creation: Promote successful dataset config to workspace template
     * - Template updates: Modify existing workspace template based on dataset learnings
     * - Data export: Extract template parameters for external systems
     * - Backup/restore: Separate template logic from calculated results
     *
     * @param valueAtRiskInfo The value-at-risk info to convert (dataset configuration)
     * @return A new RevenueAtRiskTemplateInfo with just the template parameters
     */
    public static RevenueAtRiskTemplateInfo toTemplateInfo(RevenueAtRisk valueAtRiskInfo) {
        if (valueAtRiskInfo == null) {
            return null;
        }

        return RevenueAtRiskTemplateInfo.builder()
                .customerSpendAvgAnnual(valueAtRiskInfo.getCustomerSpendAvgAnnual())
                .customerAdditionalCost(valueAtRiskInfo.getCustomerAdditionalCost())
                .numberOfCustomers(valueAtRiskInfo.getNumberOfCustomers())
                .totalYear(valueAtRiskInfo.getTotalYear())
                .employeeSalary(valueAtRiskInfo.getEmployeeSalary())
                .employeeAdditionalCost(valueAtRiskInfo.getEmployeeAdditionalCost())
                .numberOfEmployees(valueAtRiskInfo.getNumberOfEmployees())
                .scaleToTotalPeople(valueAtRiskInfo.getScaleToTotalPeople())
                .revenueAtRiskType(valueAtRiskInfo.getRevenueAtRiskType())
                .revenueAtRiskWeight(valueAtRiskInfo.getRevenueAtRiskWeight())
                .currency(valueAtRiskInfo.getCurrency())
                .build();
    }
}
