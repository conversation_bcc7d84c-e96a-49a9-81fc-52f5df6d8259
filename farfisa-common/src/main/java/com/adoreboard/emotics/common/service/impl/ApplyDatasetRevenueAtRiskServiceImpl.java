package com.adoreboard.emotics.common.service.impl;

import com.adoreboard.emotics.common.mapper.DatasetMapper;
import com.adoreboard.emotics.common.mapper.DatasetRevenueAtRiskMapper;
import com.adoreboard.emotics.common.mapper.RevenueAtRiskTemplateMapper;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRisk;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import com.adoreboard.emotics.common.service.ApplyDatasetRevenueAtRiskService;
import com.adoreboard.emotics.common.util.RevenueAtRiskMapStruct;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
/**
 * Service implementation for managing revenue-at-risk (RAR) data associated with datasets.
 * Provides methods to copy RAR data from a source dataset or workspace to a target dataset.
 */
@Service
@RequiredArgsConstructor
public class ApplyDatasetRevenueAtRiskServiceImpl implements ApplyDatasetRevenueAtRiskService {
    private static final Logger logger = LogManager.getLogger(ApplyDatasetRevenueAtRiskServiceImpl.class);

    private final DatasetMapper datasetMapper;
    private final DatasetRevenueAtRiskMapper datasetRevenueAtRiskMapper;
    private final RevenueAtRiskTemplateMapper revenueAtRiskTemplateMapper;

    /**
     * Copies revenue-at-risk (RAR) data from a source dataset to a target dataset.
     * If no RAR data is found in the source dataset, it attempts to copy from the workspace.
     *
     * @param sourceDatasetId The ID of the source dataset.
     * @param targetDatasetId The ID of the target dataset.
     */
    @Override
    public void copyDatasetRevenueAtRiskFromDataset(Integer sourceDatasetId, Integer targetDatasetId) {
        logger.info("Copying dataset revenue-at-risk from dataset {} to dataset {}", sourceDatasetId, targetDatasetId);
        // Retrieve the target dataset by its ID
        Dataset sourceDataset = datasetMapper.selectById(targetDatasetId);
        // Retrieve the target dataset by its ID
        Dataset targetDataset = datasetMapper.selectById(targetDatasetId);
        // Get the dataset's selected_rar_id field
        DatasetRevenueAtRisk sourceDatasetRevenueAtRisk = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(sourceDataset.getSelectedRarId());
        if (sourceDatasetRevenueAtRisk == null) {
            logger.info("No dataset revenue-at-risk found for dataset {}, skipping to copy from workspace", sourceDatasetId);
            copyDatasetRevenueAtRiskFromWorkspace(targetDataset.getWorkspaceId(), targetDatasetId);
            return;
        }

        // Create a new dataset revenue-at-risk entry for the target dataset using the default template
        DatasetRevenueAtRisk datasetRevenueAtRisk = DatasetRevenueAtRisk.builder()
                .datasetId(targetDatasetId)
                .templateId(sourceDatasetRevenueAtRisk.getTemplateId())
                .createdBy(targetDataset.getUserId())
                .revenueAtRisk(sourceDatasetRevenueAtRisk.getRevenueAtRisk())
                .build();

        datasetRevenueAtRiskMapper.createDatasetRevenueAtRisk(datasetRevenueAtRisk);
        datasetMapper.updateSelectedRarId(targetDatasetId, datasetRevenueAtRisk.getId());
        logger.info("Created dataset revenue-at-risk for dataset {} with id {}", targetDatasetId, datasetRevenueAtRisk.getId());
    }

    /**
     * Copies revenue-at-risk (RAR) data from a workspace to a target dataset.
     * If no default RAR template is found in the workspace, the operation is skipped.
     *
     * @param workspaceId The ID of the workspace.
     * @param targetDatasetId The ID of the target dataset.
     */
    @Override
    public void copyDatasetRevenueAtRiskFromWorkspace(Integer workspaceId, Integer targetDatasetId) {
        logger.info("Copying dataset revenue-at-risk from workspace {} to dataset {}", workspaceId, targetDatasetId);
        // Retrieve the target dataset by its ID
        Dataset dataset = datasetMapper.selectById(targetDatasetId);
        // Retrieve the default revenue-at-risk template for the workspace
        RevenueAtRiskTemplate defaultTemplate = revenueAtRiskTemplateMapper.getDefaultTemplate(workspaceId);
        if (defaultTemplate == null) {
            logger.info("No default revenue-at-risk template found for workspace {}, skipping copy", workspaceId);
            return;
        }
        // Create a new dataset revenue-at-risk entry for the target dataset using the default template
        RevenueAtRisk revenueAtRisk = RevenueAtRiskMapStruct.toRevenueAtRisk(defaultTemplate.getRevenueAtRisk());
        DatasetRevenueAtRisk datasetRevenueAtRisk = DatasetRevenueAtRisk.builder()
                .datasetId(targetDatasetId)
                .templateId(defaultTemplate.getId())
                .createdBy(dataset.getUserId())
                .revenueAtRisk(revenueAtRisk)
                .build();
        datasetRevenueAtRiskMapper.createDatasetRevenueAtRisk(datasetRevenueAtRisk);
        datasetMapper.updateSelectedRarId(targetDatasetId, datasetRevenueAtRisk.getId());
        logger.info("Created dataset revenue-at-risk for dataset {} with id {}", targetDatasetId, datasetRevenueAtRisk.getId());
    }
}
