<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2022 Adoreboard Ltd. All rights reserved.
  ~ ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="datasetCommon">

    <!--  Common results map -->

    <!--  Common sql blocks -->

    <sql id="withStatusesAsBupsm">
        bupsm AS (SELECT bulk_upload_id AS dataset_id,
                 JSONB_OBJECT_AGG(
                     status,
                     JSONB_BUILD_OBJECT(
                             'status', status,
                             'startedAt', started_at,
                             'updatedAt', updated_at,
                             'numOfItems', num_of_items,
                             'currentItem', current_item
                     )
                 ) AS progress_status_map
          FROM bulk_upload_status
          WHERE bulk_upload_id IN (SELECT id FROM datasets)
          GROUP BY dataset_id)
    </sql>

    <sql id="withStopwordsAsBusw">
        busw AS (SELECT bulk_upload_id AS dataset_id,
                    JSONB_AGG(
                        JSONB_BUILD_OBJECT(
                                'id', id,
                                'stopWord', stop_word,
                                'status', status,
                                'created', created,
                                'workspaceId', workspace_id
                        )
                    ) AS stop_words
             FROM stop_word
             WHERE bulk_upload_id IN (SELECT id FROM datasets)
             GROUP BY dataset_id)
    </sql>

    <sql id="withDownloadDetailsAsDdd">
        ddd AS (SELECT *
                FROM dataset_download_details
                WHERE dataset_id IN (SELECT id FROM datasets)
        )
    </sql>

    <sql id="withTagsAsDt">
        dt AS (
            SELECT dt.dataset_id,
                   array_agg(dt.tag_id) as tag_ids
            FROM bulk_upload_dataset_tag dt
            WHERE dataset_id IN (SELECT id FROM datasets)
            GROUP BY dt.dataset_id
        )
    </sql>

    <sql id="withPermissionsAsDp">
        dp AS (
            SELECT *
            FROM dataset_permission
            WHERE dataset_id IN (SELECT id FROM datasets)
        )
    </sql>

    <sql id="withStorytellerReport">
        rp AS (
            SELECT COUNT(id) as report_count, dataset_id
            FROM storyteller_report
            WHERE dataset_id IN (SELECT id FROM datasets)
            GROUP BY dataset_id
        )
    </sql>

    <sql id="withRevenueAtRisk">
        rar AS (
        SELECT
            dar.id as rar_id,
            dar.dataset_id as rar_dataset_id,
            COALESCE(rt.name, dar.name) as rar_name,
            dar.template_id as rar_template_id,
            dar.created_by as rar_created_by,
            dar.revenue_at_risk as rar_revenue_at_risk,
            dar.created_at as rar_created_at,
            dar.updated_at as rar_updated_at,
            u.id as u_id,
            u.login_name as u_loginName,
            u.first_name as u_firstName,
            u.last_name as u_lastName
        FROM dataset_revenue_at_risk dar
        INNER JOIN bulk_upload bu ON bu.selected_rar_id = dar.id
        INNER JOIN users u ON dar.created_by = u.id
        LEFT JOIN revenue_at_risk_templates rt ON dar.template_id = rt.id
        WHERE dar.dataset_id IN (SELECT id FROM datasets)
    )
    </sql>

    <!--
        To use this view, you need to provide a CTE datasets which is a list of bulk_upload.
        See dataset_mapper.xml for references
     -->
    <sql id="bulk_upload_view">
        <include refid="datasetCommon.withStatusesAsBupsm"/>,
        <include refid="datasetCommon.withStopwordsAsBusw"/>,
        <include refid="datasetCommon.withDownloadDetailsAsDdd"/>,
        <include refid="datasetCommon.withTagsAsDt"/>,
        <include refid="datasetCommon.withPermissionsAsDp"/>,
        <include refid="datasetCommon.withStorytellerReport"/>
        SELECT bu.*,
            (bu.summary ->> 'polarity') :: DOUBLE PRECISION AS polarity,
            (bu.summary ->> 'adoreScore') :: NUMERIC        AS adorescore,
            bu.rating_scale_maps                            AS rating_scale_maps,
            bu.metric_calculation_settings                  AS metric_calculation_settings,
            bu.dataset_domain                               AS dastaset_domain,
            bupsm.progress_status_map                       AS progress_status_map,
            busw.stop_words                                 AS stop_words,
            ddd.analysis_download_location                  AS analysis_download_location,
            ddd.original_download_location                  AS original_download_location,
            dt.tag_ids                                      AS tag_ids,
            rp.report_count                                 AS report_count,
            dp.*
        FROM datasets bu
            LEFT OUTER JOIN bupsm ON bupsm.dataset_id = bu.id
            LEFT OUTER JOIN busw ON busw.dataset_id = bu.id
            LEFT OUTER JOIN ddd ON ddd.dataset_id = bu.id
            LEFT OUTER JOIN dt ON dt.dataset_id = bu.id
            LEFT OUTER JOIN dp ON dp.dataset_id = bu.id
            LEFT OUTER JOIN rp ON rp.dataset_id = bu.id
    </sql>
</mapper>