-- <PERSON><PERSON>t to rename JSONB property from insightSettings.displayValueAtRisk to insightSettings.displayRevenueAtRisk

-- Update storyteller_report table to rename JSONB properties
-- 1. insightSettings.displayValueAtRisk -> insightSettings.displayRevenueAtRisk
-- 2. valueAtRiskInfo -> revenueAtRisk
-- 3. Within revenueAtRisk object:
--    - valueAtRiskType -> revenueAtRiskType
--    - revenueAtRiskWeight -> revenueAtRiskWeight
--    - valueAtRiskAmount -> revenueAtRiskAmount
--    - valueAtRiskAmountString -> revenueAtRiskAmountString

-- Single update approach - handles both renames at once
UPDATE storyteller_report
SET settings = (
    -- Start with original settings
    settings
        -- Remove the old top-level key if it exists
        - 'valueAtRiskInfo'
        -- Remove the old nested key by reconstructing insightSettings
        - 'insightSettings'
        -- Add back insightSettings with renamed property
        || CASE
               WHEN settings ? 'insightSettings' THEN
                   jsonb_build_object(
                           'insightSettings',
                           (settings->'insightSettings') - 'displayValueAtRisk' ||
                           CASE
                               WHEN (settings->'insightSettings') ? 'displayValueAtRisk' THEN
                                   jsonb_build_object('displayRevenueAtRisk', settings->'insightSettings'->'displayValueAtRisk')
                               ELSE '{}'::jsonb
                               END
                   )
               ELSE '{}'::jsonb
        END
        -- Add the renamed top-level property
        || CASE
               WHEN settings ? 'valueAtRiskInfo' THEN
                   jsonb_build_object('revenueAtRisk', settings->'valueAtRiskInfo')
               ELSE '{}'::jsonb
        END
    )
WHERE settings ? 'valueAtRiskInfo' OR settings->'insightSettings' ? 'displayValueAtRisk';

-- Update properties inside the revenueAtRisk object in storyteller_report table
-- Rename:
--    - valueAtRiskType -> revenueAtRiskType
--    - revenueAtRiskWeight -> revenueAtRiskWeight
--    - valueAtRiskAmount -> revenueAtRiskAmount
--    - valueAtRiskAmountString -> revenueAtRiskAmountString

WITH updated_revenue_settings AS (
    SELECT
        id,
        settings,
        -- Update properties within the revenueAtRisk object
        (settings->'revenueAtRisk')
            - 'valueAtRiskType'
            - 'revenueAtRiskWeight'
            - 'valueAtRiskAmount'
            - 'valueAtRiskAmountString'
            || CASE WHEN (settings->'revenueAtRisk') ? 'valueAtRiskType' THEN
                        jsonb_build_object('revenueAtRiskType', settings->'revenueAtRisk'->'valueAtRiskType')
                    ELSE '{}'::jsonb END
            || CASE WHEN (settings->'revenueAtRisk') ? 'revenueAtRiskWeight' THEN
                        jsonb_build_object('revenueAtRiskWeight', settings->'revenueAtRisk'->'revenueAtRiskWeight')
                    ELSE '{}'::jsonb END
            || CASE WHEN (settings->'revenueAtRisk') ? 'valueAtRiskAmount' THEN
                        jsonb_build_object('revenueAtRiskAmount', settings->'revenueAtRisk'->'valueAtRiskAmount')
                    ELSE '{}'::jsonb END
            || CASE WHEN (settings->'revenueAtRisk') ? 'valueAtRiskAmountString' THEN
                        jsonb_build_object('revenueAtRiskAmountString', settings->'revenueAtRisk'->'valueAtRiskAmountString')
                    ELSE '{}'::jsonb END
            AS new_revenue_at_risk_data
    FROM storyteller_report
    WHERE settings ? 'revenueAtRisk'
      AND (
        settings->'revenueAtRisk' ? 'valueAtRiskType'
            OR settings->'revenueAtRisk' ? 'revenueAtRiskWeight'
            OR settings->'revenueAtRisk' ? 'valueAtRiskAmount'
            OR settings->'revenueAtRisk' ? 'valueAtRiskAmountString'
        )
)
UPDATE storyteller_report
SET settings = jsonb_set(
        storyteller_report.settings,
        '{revenueAtRisk}',
        updated_revenue_settings.new_revenue_at_risk_data
               )
FROM updated_revenue_settings
WHERE storyteller_report.id = updated_revenue_settings.id;

-- Update dataset_revenue_at_risk table to rename JSONB properties
-- Within revenue_at_risk column:
--    - valueAtRiskType -> revenueAtRiskType
--    - valueAtRiskAmount -> revenueAtRiskAmount
--    - revenueAtRiskWeight -> revenueAtRiskWeight
--    - valueAtRiskAmountString -> revenueAtRiskAmountString

WITH updated_revenue_at_risk AS (
    SELECT
        id,
        revenue_at_risk,
        -- Rename properties within the revenue_at_risk object
        revenue_at_risk
            - 'valueAtRiskType'
            - 'valueAtRiskAmount'
            - 'revenueAtRiskWeight'
            - 'valueAtRiskAmountString'
            || CASE WHEN revenue_at_risk ? 'valueAtRiskType' THEN
                        jsonb_build_object('revenueAtRiskType', revenue_at_risk->'valueAtRiskType')
                    ELSE '{}'::jsonb END
            || CASE WHEN revenue_at_risk ? 'valueAtRiskAmount' THEN
                        jsonb_build_object('revenueAtRiskAmount', revenue_at_risk->'valueAtRiskAmount')
                    ELSE '{}'::jsonb END
            || CASE WHEN revenue_at_risk ? 'revenueAtRiskWeight' THEN
                        jsonb_build_object('revenueAtRiskWeight', revenue_at_risk->'revenueAtRiskWeight')
                    ELSE '{}'::jsonb END
            || CASE WHEN revenue_at_risk ? 'valueAtRiskAmountString' THEN
                        jsonb_build_object('revenueAtRiskAmountString', revenue_at_risk->'valueAtRiskAmountString')
                    ELSE '{}'::jsonb END
            AS new_revenue_at_risk_data
    FROM dataset_revenue_at_risk
    WHERE revenue_at_risk ? 'valueAtRiskType'
       OR revenue_at_risk ? 'valueAtRiskAmount'
       OR revenue_at_risk ? 'revenueAtRiskWeight'
       OR revenue_at_risk ? 'valueAtRiskAmountString'
)
UPDATE dataset_revenue_at_risk
SET revenue_at_risk = updated_revenue_at_risk.new_revenue_at_risk_data,
    updated_at = CURRENT_TIMESTAMP
FROM updated_revenue_at_risk
WHERE dataset_revenue_at_risk.id = updated_revenue_at_risk.id;