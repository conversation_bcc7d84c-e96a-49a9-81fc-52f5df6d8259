package com.adoreboard.emotics.common.mapper;

import com.adoreboard.emotics.common.enums.RevenueAtRiskType;
import com.adoreboard.emotics.common.enums.RevenueAtRiskWeight;
import com.adoreboard.emotics.common.model.enums.RarCurrency;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRisk;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplateInfo;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

public class DatasetRevenueAtRiskMapStructTest extends AbstractMapperTest {
    @Autowired
    private DatasetRevenueAtRiskMapper datasetRevenueAtRiskMapper;

    @Autowired
    private RevenueAtRiskTemplateMapper revenueAtRiskTemplateMapper;

    @Before
    public void setup() throws Exception {
        insertPostgreSqlTestData();
    }

    @After
    public void tearDown() throws Exception {
        deletePostgreSqlTestData();
    }

    @Test
    public void shouldCreateDatasetRevenueAtRisk() {
        // Given
        RevenueAtRisk varInfo = createTestValueAtRiskInfo();
        DatasetRevenueAtRisk datasetRevenueAtRisk = DatasetRevenueAtRisk.builder()
                .datasetId(1000)
                .templateId(null) // No linked template
                .createdBy(98)
                .revenueAtRisk(varInfo)
                .createdAt(LocalDateTime.now())
                .build();

        // When
        Integer createdId = datasetRevenueAtRiskMapper.createDatasetRevenueAtRisk(datasetRevenueAtRisk);

        // Then
        assertNotNull(datasetRevenueAtRisk.getId());
        assertNotNull(createdId);

        DatasetRevenueAtRisk created = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(datasetRevenueAtRisk.getId());
        assertNotNull(created);
        assertEquals(Integer.valueOf(1000), created.getDatasetId());
        assertNull(created.getTemplateId());
        assertEquals(98, created.getCreatedBy());
        assertNotNull(created.getRevenueAtRisk());
        assertEquals(RevenueAtRiskType.CUSTOMER, created.getRevenueAtRisk().getRevenueAtRiskType());
        assertEquals(new BigDecimal("1500.00"), created.getRevenueAtRisk().getRevenueAtRiskAmount());
    }

    @Test
    public void shouldCreateDatasetRevenueAtRiskWithTemplate() {
        // Given
        RevenueAtRiskTemplate template = createTestTemplate("Test Template", 99);
        RevenueAtRisk varInfo = createTestValueAtRiskInfo();
        DatasetRevenueAtRisk datasetRevenueAtRisk = DatasetRevenueAtRisk.builder()
                .datasetId(1001)
                .templateId(template.getId())
                .createdBy(98)
                .revenueAtRisk(varInfo)
                .createdAt(LocalDateTime.now())
                .build();

        // When
        datasetRevenueAtRiskMapper.createDatasetRevenueAtRisk(datasetRevenueAtRisk);

        // Then
        assertNotNull(datasetRevenueAtRisk.getId());

        DatasetRevenueAtRisk created = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(datasetRevenueAtRisk.getId());
        assertNotNull(created);
        assertEquals(Integer.valueOf(1001), created.getDatasetId());
        assertEquals(template.getId(), created.getTemplateId());
        assertEquals(98, created.getCreatedBy());
    }

    @Test
    public void shouldGetDatasetRevenueAtRisks() {
        // Given
        DatasetRevenueAtRisk template1 = createTestDatasetRevenueAtRisk(1000, null, 98);
        DatasetRevenueAtRisk template2 = createTestDatasetRevenueAtRisk(1000, null, 99);
        DatasetRevenueAtRisk template3 = createTestDatasetRevenueAtRisk(1001, null, 98); // Different dataset

        // When
        List<DatasetRevenueAtRisk> templates = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisks(1000);

        // Then
        assertEquals(2, templates.size());

        // Should be ordered by created_at ASC
        assertEquals(template1.getId(), templates.get(0).getId());
        assertEquals(template2.getId(), templates.get(1).getId());
    }

    @Test
    public void shouldGetDatasetRevenueAtRiskByTemplateId() {
        // Given
        RevenueAtRiskTemplate workspaceTemplate = createTestTemplate("Workspace Template", 99);
        DatasetRevenueAtRisk datasetTemplate = createTestDatasetRevenueAtRisk(1000, workspaceTemplate.getId(), 98);

        // When
        DatasetRevenueAtRisk found = datasetRevenueAtRiskMapper.getDatasetRevenueAtRiskByTemplateId(1000, workspaceTemplate.getId());

        // Then
        assertNotNull(found);
        assertEquals(datasetTemplate.getId(), found.getId());
        assertEquals(Integer.valueOf(1000), found.getDatasetId());
        assertEquals(workspaceTemplate.getId(), found.getTemplateId());
    }

    @Test
    public void shouldReturnNullWhenTemplateNotFound() {
        // When
        DatasetRevenueAtRisk notFound = datasetRevenueAtRiskMapper.getDatasetRevenueAtRiskByTemplateId(999, 999);

        // Then
        assertNull(notFound);
    }

    @Test
    public void shouldUpdateDatasetRevenueAtRisk() {
        // Given
        DatasetRevenueAtRisk original = createTestDatasetRevenueAtRisk(1000, null, 98);

        // When
        RevenueAtRisk updatedVarInfo = createTestValueAtRiskInfo();
        updatedVarInfo.setRevenueAtRiskAmount(new BigDecimal("2500.00"));
        updatedVarInfo.setRevenueAtRiskType(RevenueAtRiskType.EMPLOYEE);
        updatedVarInfo.setSelectedThemeIds(Arrays.asList(1, 2, 3));

        original.setRevenueAtRisk(updatedVarInfo);
        datasetRevenueAtRiskMapper.updateDatasetRevenueAtRisk(original);

        // Then
        DatasetRevenueAtRisk updated = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(original.getId());
        assertEquals(new BigDecimal("2500.00"), updated.getRevenueAtRisk().getRevenueAtRiskAmount());
        assertEquals(RevenueAtRiskType.EMPLOYEE, updated.getRevenueAtRisk().getRevenueAtRiskType());
        assertEquals(Arrays.asList(1, 2, 3), updated.getRevenueAtRisk().getSelectedThemeIds());
    }

    @Test
    public void shouldDeleteDatasetRevenueAtRisk() {
        // Given
        DatasetRevenueAtRisk template = createTestDatasetRevenueAtRisk(1000, null, 98);
        Integer templateId = template.getId();

        // When
        datasetRevenueAtRiskMapper.deleteDatasetRevenueAtRisk(templateId);

        // Then
        DatasetRevenueAtRisk deleted = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(templateId);
        assertNull(deleted);
    }

    @Test
    public void shouldDeleteDatasetTemplatesByDatasetId() {
        // Given
        createTestDatasetRevenueAtRisk(1000, null, 98);
        createTestDatasetRevenueAtRisk(1000, null, 99);
        createTestDatasetRevenueAtRisk(1001, null, 98); // Different dataset

        // When
        datasetRevenueAtRiskMapper.deleteDatasetTemplatesByDatasetId(1000);

        // Then
        List<DatasetRevenueAtRisk> remainingTemplates = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisks(1000);
        assertEquals(0, remainingTemplates.size());

        List<DatasetRevenueAtRisk> otherDatasetTemplates = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisks(1001);
        assertEquals(1, otherDatasetTemplates.size());
    }

    @Test
    public void shouldCountDatasetTemplates() {
        // Given
        createTestDatasetRevenueAtRisk(1000, null, 98);
        createTestDatasetRevenueAtRisk(1000, null, 99);
        createTestDatasetRevenueAtRisk(1001, null, 98); // Different dataset

        // When
        int count1000 = datasetRevenueAtRiskMapper.countDatasetTemplates(1000);
        int count1001 = datasetRevenueAtRiskMapper.countDatasetTemplates(1001);
        int count999 = datasetRevenueAtRiskMapper.countDatasetTemplates(999); // Non-existent

        // Then
        assertEquals(2, count1000);
        assertEquals(1, count1001);
        assertEquals(0, count999);
    }

    @Test
    public void shouldCloneWorkspaceTemplatesForDataset() {
        // Given
        RevenueAtRiskTemplate template1 = createTestTemplate("Template 1", 99);
        RevenueAtRiskTemplate template2 = createTestTemplate("Template 2", 99);
        createTestTemplate("Template 3", 98); // Different workspace
        int datasetId = 1000;
        // When
        datasetRevenueAtRiskMapper.cloneWorkspaceTemplatesForDataset(datasetId, 99);

        // Then
        List<DatasetRevenueAtRisk> clonedTemplates = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisks(datasetId);
        assertEquals(2, clonedTemplates.size());

        // Find cloned templates by template ID (order is not guaranteed)
        DatasetRevenueAtRisk clonedFromTemplate1 = clonedTemplates.stream()
                .filter(t -> template1.getId().equals(t.getTemplateId()))
                .findFirst()
                .orElse(null);

        DatasetRevenueAtRisk clonedFromTemplate2 = clonedTemplates.stream()
                .filter(t -> template2.getId().equals(t.getTemplateId()))
                .findFirst()
                .orElse(null);

        // Verify the cloned templates have correct properties
        assertNotNull("Cloned template from template1 should exist", clonedFromTemplate1);
        assertEquals(datasetId, clonedFromTemplate1.getDatasetId().intValue());
        assertEquals(template1.getId(), clonedFromTemplate1.getTemplateId());
        assertEquals(template1.getCreatedBy(), clonedFromTemplate1.getCreatedBy()); // Should use template creator
        assertNotNull(clonedFromTemplate1.getRevenueAtRisk());

        assertNotNull("Cloned template from template2 should exist", clonedFromTemplate2);
        assertEquals(datasetId, clonedFromTemplate2.getDatasetId().intValue());
        assertEquals(template2.getId(), clonedFromTemplate2.getTemplateId());
        assertEquals(template2.getCreatedBy(), clonedFromTemplate2.getCreatedBy());
        assertNotNull(clonedFromTemplate2.getRevenueAtRisk());
    }

    @Test
    public void shouldHandleComplexValueAtRiskInfo() {
        // Given - Complex ValueAtRiskInfo with all fields populated
        RevenueAtRisk complexVarInfo = new RevenueAtRisk();
        complexVarInfo.setRevenueAtRiskType(RevenueAtRiskType.EMPLOYEE);
        complexVarInfo.setRevenueAtRiskWeight(RevenueAtRiskWeight.HIGH);
        complexVarInfo.setCurrency(RarCurrency.EUR);
        complexVarInfo.setCustomerSpendAvgAnnual(10000.0);
        complexVarInfo.setCustomerAdditionalCost(200.0);
        complexVarInfo.setNumberOfCustomers(1000);
        complexVarInfo.setTotalYear(2);
        complexVarInfo.setEmployeeSalary(50000.0);
        complexVarInfo.setEmployeeAdditionalCost(100000.0);
        complexVarInfo.setNumberOfEmployees(50);
        complexVarInfo.setScaleToTotalPeople(true);
        complexVarInfo.setRevenueAtRiskAmount(new BigDecimal("75000.00"));
        complexVarInfo.setSelectedThemeIds(Arrays.asList(1, 2, 3, 4, 5));

        DatasetRevenueAtRisk datasetTemplate = DatasetRevenueAtRisk.builder()
                .datasetId(1000)
                .templateId(null)
                .createdBy(98)
                .revenueAtRisk(complexVarInfo)
                .createdAt(LocalDateTime.now())
                .build();

        // When
        datasetRevenueAtRiskMapper.createDatasetRevenueAtRisk(datasetTemplate);

        // Then
        DatasetRevenueAtRisk retrieved = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(datasetTemplate.getId());
        RevenueAtRisk retrievedVarInfo = retrieved.getRevenueAtRisk();

        assertEquals(complexVarInfo.getRevenueAtRiskType(), retrievedVarInfo.getRevenueAtRiskType());
        assertEquals(complexVarInfo.getRevenueAtRiskWeight(), retrievedVarInfo.getRevenueAtRiskWeight());
        assertEquals(complexVarInfo.getCurrency(), retrievedVarInfo.getCurrency());
        assertEquals(complexVarInfo.getCustomerSpendAvgAnnual(), retrievedVarInfo.getCustomerSpendAvgAnnual());
        assertEquals(complexVarInfo.getCustomerAdditionalCost(), retrievedVarInfo.getCustomerAdditionalCost());
        assertEquals(complexVarInfo.getNumberOfCustomers(), retrievedVarInfo.getNumberOfCustomers());
        assertEquals(complexVarInfo.getTotalYear(), retrievedVarInfo.getTotalYear());
        assertEquals(complexVarInfo.getEmployeeSalary(), retrievedVarInfo.getEmployeeSalary());
        assertEquals(complexVarInfo.getEmployeeAdditionalCost(), retrievedVarInfo.getEmployeeAdditionalCost());
        assertEquals(complexVarInfo.getNumberOfEmployees(), retrievedVarInfo.getNumberOfEmployees());
        assertEquals(complexVarInfo.getScaleToTotalPeople(), retrievedVarInfo.getScaleToTotalPeople());
        assertEquals(new BigDecimal("75000.00"), retrievedVarInfo.getRevenueAtRiskAmount());
        assertEquals(Arrays.asList(1, 2, 3, 4, 5), retrievedVarInfo.getSelectedThemeIds());
    }

    // Helper methods
    private DatasetRevenueAtRisk createTestDatasetRevenueAtRisk(Integer datasetId, Integer templateId, Integer createdBy) {
        RevenueAtRisk varInfo = createTestValueAtRiskInfo();
        DatasetRevenueAtRisk datasetTemplate = DatasetRevenueAtRisk.builder()
                .datasetId(datasetId)
                .templateId(templateId)
                .createdBy(createdBy)
                .revenueAtRisk(varInfo)
                .createdAt(LocalDateTime.now())
                .build();

        datasetRevenueAtRiskMapper.createDatasetRevenueAtRisk(datasetTemplate);
        return datasetTemplate;
    }

    private RevenueAtRiskTemplate createTestTemplate(String name, Integer workspaceId) {
        RevenueAtRiskTemplateInfo varInfo = createTestRevenueAtRiskTemplateInfo();
        RevenueAtRiskTemplate template = RevenueAtRiskTemplate.builder()
                .name(name)
                .workspaceId(workspaceId)
                .createdBy(98)
                .revenueAtRisk(varInfo)
                .defaultTemplate(false)
                .createdAt(LocalDateTime.now())
                .build();

        revenueAtRiskTemplateMapper.createWorkspaceTemplate(template);
        return template;
    }

    private RevenueAtRisk createTestValueAtRiskInfo() {
        RevenueAtRisk varInfo = new RevenueAtRisk();
        varInfo.setRevenueAtRiskType(RevenueAtRiskType.CUSTOMER);
        varInfo.setRevenueAtRiskWeight(RevenueAtRiskWeight.MEDIUM);
        varInfo.setCurrency(RarCurrency.USD);
        varInfo.setCustomerSpendAvgAnnual(500.0);
        varInfo.setCustomerAdditionalCost(1000.0);
        varInfo.setNumberOfCustomers(10);
        varInfo.setTotalYear(1);
        varInfo.setScaleToTotalPeople(false);
        varInfo.setRevenueAtRiskAmount(new BigDecimal("1500.00"));
        varInfo.setSelectedThemeIds(Arrays.asList(1, 2));
        return varInfo;
    }

    private RevenueAtRiskTemplateInfo createTestRevenueAtRiskTemplateInfo() {
        RevenueAtRiskTemplateInfo varInfo = new RevenueAtRiskTemplateInfo();
        varInfo.setRevenueAtRiskType(RevenueAtRiskType.CUSTOMER);
        varInfo.setRevenueAtRiskWeight(RevenueAtRiskWeight.MEDIUM);
        varInfo.setCurrency(RarCurrency.USD);
        varInfo.setCustomerSpendAvgAnnual(500.0);
        varInfo.setCustomerAdditionalCost(1000.0);
        varInfo.setNumberOfCustomers(10);
        varInfo.setTotalYear(1);
        varInfo.setScaleToTotalPeople(false);
        return varInfo;
    }
}