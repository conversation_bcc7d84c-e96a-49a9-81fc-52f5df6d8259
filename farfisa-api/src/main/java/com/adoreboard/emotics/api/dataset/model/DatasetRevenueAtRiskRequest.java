package com.adoreboard.emotics.api.dataset.model;

import lombok.Getter;
import lombok.Setter;

/**
 * Request DTO for dataset revenue at risk operations that includes name field.
 */
@Getter
@Setter
public class DatasetRevenueAtRiskRequest extends DatasetRevenueAtRiskPreviewRequest {
    private String name;
    private Integer rarId; // Optional, used for update operations, if it's empty, will use the selected one
}
