package com.adoreboard.emotics.api.dataset.service;

import com.adoreboard.emotics.api.dataset.model.DatasetRevenueAtRiskPreviewRequest;
import com.adoreboard.emotics.api.dataset.model.DatasetRevenueAtRiskRequest;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;

import java.util.List;

public interface DatasetRevenueAtRiskService {

    // Dataset RARs operations
    List<DatasetRevenueAtRisk> getDatasetRevenueAtRisks(User user, Integer datasetId);

    DatasetRevenueAtRisk getDatasetRevenueAtRisk(User user, Integer datasetId, Integer datasetRarId);


    DatasetRevenueAtRisk createDatasetRevenueAtRisk(User user, DatasetRevenueAtRisk datasetRar);

    DatasetRevenueAtRisk createDatasetRevenueAtRisk(User user,  Integer datasetId, DatasetRevenueAtRiskRequest datasetRar);

    DatasetRevenueAtRisk updateDatasetRevenueAtRisk(User user, Integer datasetId, DatasetRevenueAtRiskRequest datasetRar);

    DatasetRevenueAtRisk updateDatasetLinkedTemplate(User user, Integer datasetId, Integer rarId, Integer newTemplateId);

    DatasetRevenueAtRisk updateDatasetRevenueAtRiskData(User user, DatasetRevenueAtRisk datasetRevenueAtRisk);

    DatasetRevenueAtRisk previewDatasetRevenueAtRisk(User user, Integer datasetId, Integer rarId, DatasetRevenueAtRiskPreviewRequest request);

    void deleteDatasetRevenueAtRisk(User user, Integer datasetRarId);

    // Dataset selected rar operations
    Integer getSelectedRevenueAtRiskIdForDataset(User user, Integer datasetId);

    DatasetRevenueAtRisk getDatasetSelectedRevenueAtRisk(User user, Integer datasetId);

    DatasetRevenueAtRisk getDatasetSelectedRevenueAtRisk(User user, Integer datasetId, Boolean isCreateNew);

    DatasetRevenueAtRisk applyTemplateForDatasetSelectedRevenueAtRisk(User user, Integer datasetId, Integer newTemplateId);

}
