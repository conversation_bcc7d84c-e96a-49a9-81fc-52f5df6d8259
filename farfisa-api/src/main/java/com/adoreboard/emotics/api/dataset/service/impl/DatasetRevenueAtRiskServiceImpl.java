package com.adoreboard.emotics.api.dataset.service.impl;

import com.adoreboard.emotics.api.dataset.model.DatasetRevenueAtRiskPreviewRequest;
import com.adoreboard.emotics.api.dataset.model.DatasetRevenueAtRiskRequest;
import com.adoreboard.emotics.api.dataset.service.DatasetRevenueAtRiskService;
import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.RevenueAtRiskTemplateService;
import com.adoreboard.emotics.api.revenueAtRisk.service.RevenueAtRiskService;
import com.adoreboard.emotics.common.enums.RevenueAtRiskType;
import com.adoreboard.emotics.common.enums.RevenueAtRiskWeight;
import com.adoreboard.emotics.common.exception.EmoticsException;
import com.adoreboard.emotics.common.mapper.DatasetMapper;
import com.adoreboard.emotics.common.mapper.DatasetRevenueAtRiskMapper;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRisk;
import com.adoreboard.emotics.common.service.ApplyDatasetRevenueAtRiskService;
import com.adoreboard.emotics.common.util.RevenueAtRiskMapStruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <pre>
 * Service implementation for managing dataset-specific revenue-at-risk configurations.
 *
 * BUSINESS OVERVIEW:
 * This service manages the relationship between datasets and their revenue-at-risk (RAR) configurations.
 * It bridges workspace-level templates with dataset-specific risk calculations.
 * </pre>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DatasetRevenueAtRiskServiceImpl implements DatasetRevenueAtRiskService {
    private final DatasetMapper datasetMapper;
    private final DatasetRevenueAtRiskMapper datasetRevenueAtRiskMapper;
    private final RevenueAtRiskTemplateService revenueAtRiskTemplateService;
    private final RevenueAtRiskService revenueAtRiskService;
    private final ApplyDatasetRevenueAtRiskService applyDatasetRevenueAtRiskService;

    /**
     * Retrieves all revenue-at-risk templates associated with a specific dataset.
     *
     * @param user The authenticated user requesting the data
     * @param datasetId The ID of the dataset to retrieve templates for
     * @return List of all revenue-at-risk templates for the dataset
     * @throws EmoticsException if user doesn't have access to the dataset
     */
    @Override
    public List<DatasetRevenueAtRisk> getDatasetRevenueAtRisks(User user, Integer datasetId) {
        validateUserAccess(user, datasetId);
        return datasetRevenueAtRiskMapper.getDatasetRevenueAtRisks(datasetId);
    }

    /**
     * <pre>
     * Retrieves a specific dataset revenue-at-risk configuration by its ID.
     * - For linked templates, it fetches the template data and applies it to the configuration.
     * - For custom configurations, it returns the raw data without template linkage.
     * Always recalculates the revenue-at-risk amount before returning.
     * @param user The authenticated user requesting the data
     * @param rarId The ID of the specific dataset revenue-at-risk configuration
     * @return The dataset revenue-at-risk configuration with calculated values and template name (if linked)
     * @throws EmoticsException if RAR not found or user lacks access
     * </pre>
     */
    @Override
    public DatasetRevenueAtRisk getDatasetRevenueAtRisk(User user, Integer datasetId, Integer rarId) {
        validateUserAccess(user, datasetId);
        DatasetRevenueAtRisk datasetRevenueAtRisk = getOrCreateDatasetRevenueAtRisk(datasetId, rarId);
        applyTemplateData(user, datasetRevenueAtRisk);
        if (datasetRevenueAtRisk.getRevenueAtRisk() != null) {
            updateDatasetRevenueAtRisk(user, datasetRevenueAtRisk, true);
        }
        return datasetRevenueAtRisk;
    }

    /**
     * <pre>
     * Retrieves the currently selected/active revenue-at-risk configuration for a dataset.
     *
     * @param user The authenticated user requesting the data
     * @param datasetId The ID of the dataset
     * @return The currently selected revenue-at-risk configuration for the dataset
     * </pre>
     */
    @Override
    public DatasetRevenueAtRisk getDatasetSelectedRevenueAtRisk(User user, Integer datasetId) {
        validateUserAccess(user, datasetId);
        Dataset dataset = getDatasetById(datasetId);
        return this.getDatasetRevenueAtRisk(user, datasetId, dataset.getSelectedRarId());
    }

    /**
     * <pre>
     * Retrieves the currently selected/active revenue-at-risk configuration for a dataset.
     *
     * @param user The authenticated user requesting the data
     * @param datasetId The ID of the dataset
     * @return The currently selected revenue-at-risk configuration for the dataset
     * </pre>
     */
    @Override
    public DatasetRevenueAtRisk getDatasetSelectedRevenueAtRisk(User user, Integer datasetId, Boolean isCreateNew) {
        if(isCreateNew != null && isCreateNew) {
            return this.initCustomDatasetRevenueAtRisk(user, datasetId);
        }
        return this.getDatasetSelectedRevenueAtRisk(user, datasetId);
    }

    /**
     * <pre>
     * Creates a new revenue-at-risk configuration for a dataset.
     *
     * @param user The authenticated user creating the configuration
     * @param datasetRevenueAtRisk The RAR configuration to create (with risk parameters)
     * @return The created configuration with calculated risk amounts
     * @throws EmoticsException if user lacks access or validation fails
     * </pre>
     */
    @Override
    public DatasetRevenueAtRisk createDatasetRevenueAtRisk(User user, DatasetRevenueAtRisk datasetRevenueAtRisk) {
        // Validate user has access to dataset
        validateUserAccess(user, datasetRevenueAtRisk.getDatasetId());

        datasetRevenueAtRisk.setCreatedBy(user.getId());
        updateTimestamps(datasetRevenueAtRisk, true);

        // BUSINESS RULE: Auto-calculate risk amounts based on dataset content
        // Calculate Revenue At Risk for the new template
        recalculateIfNeeded(datasetRevenueAtRisk, true);

        datasetRevenueAtRiskMapper.createDatasetRevenueAtRisk(datasetRevenueAtRisk);
        return datasetRevenueAtRisk;
    }

    @Override
    public DatasetRevenueAtRisk createDatasetRevenueAtRisk(User user, Integer datasetId, DatasetRevenueAtRiskRequest datasetRar) {
        if(datasetRar.getTemplateId() != null) {
            return this.applyTemplateForDatasetSelectedRevenueAtRisk(user, datasetId, datasetRar.getTemplateId());
        }
        DatasetRevenueAtRisk newDatasetRevenueAtRisk = DatasetRevenueAtRisk.builder()
                .datasetId(datasetId)
                .revenueAtRisk(datasetRar.getRevenueAtRisk() == null ? new RevenueAtRisk(RevenueAtRiskType.CUSTOMER, RevenueAtRiskWeight.MEDIUM) : datasetRar.getRevenueAtRisk())
                .createdBy(user.getId())
                .name(datasetRar.getName() == null ? "Dataset Custom Calculation" : datasetRar.getName())
                .build();
        return this.createDatasetRevenueAtRisk(user, newDatasetRevenueAtRisk);
    }

    /**
     * <pre>
     * Updates an existing revenue-at-risk configuration (without recalculation).
     *
     * Business Logic:
     * - Allows users to modify RAR parameters without triggering expensive recalculations
     * - Useful for quick parameter adjustments or metadata updates
     * - Delegates to the overloaded method with recalculate=false
     * - Preserves existing calculated values unless explicitly recalculated
     * </pre>
     */
    @Override
    public DatasetRevenueAtRisk updateDatasetRevenueAtRisk(User user, Integer datasetId, DatasetRevenueAtRiskRequest request) {
        Dataset dataset = getDatasetById(datasetId);
        Integer rarId = request.getRarId() == null ? dataset.getSelectedRarId() : request.getRarId();
        if (rarId == null) {
           return this.createDatasetRevenueAtRisk(user, datasetId, request);
        }
        DatasetRevenueAtRisk datasetRevenueAtRisk = validateAndGetDatasetRevenueAtRisk(user, rarId);
        applyUpdateFromRequest(datasetRevenueAtRisk, request, user);
        updateTimestamps(datasetRevenueAtRisk, false);

        return updateDatasetRevenueAtRisk(user, datasetRevenueAtRisk, false);
    }

    /**
     * <pre>
     *
     * Updates an existing revenue-at-risk configuration with optional recalculation.
     *
     * Business Logic:
     * - Provides flexibility to update RAR configurations with or without recalculation
     * - Recalculation is expensive (analyzes entire dataset) so it's optional
     * - When recalculate=true: Re-analyzes dataset content and updates risk amounts
     * - When recalculate=false: Only updates the configuration parameters
     * - Used internally by other methods and directly by API when explicit control is needed
     *
     * @param user The authenticated user updating the configuration
     * @param datasetRevenueAtRisk The updated RAR configuration
     * @param recalculate Whether to recalculate risk amounts based on current dataset content
     * @return The updated configuration with optionally recalculated values
     * </pre>
     */
    public DatasetRevenueAtRisk updateDatasetRevenueAtRisk(User user, DatasetRevenueAtRisk datasetRevenueAtRisk, boolean recalculate) {
        updateTimestamps(datasetRevenueAtRisk, false);

        // BUSINESS RULE: Conditional recalculation for performance optimization
        // Recalculate Revenue At Risk for the updated template
        recalculateIfNeeded(datasetRevenueAtRisk, recalculate);

        datasetRevenueAtRiskMapper.updateDatasetRevenueAtRisk(datasetRevenueAtRisk);
        return datasetRevenueAtRisk;
    }

    /**
     * <pre>
     * Updates the template association for a dataset revenue-at-risk configuration.
     *
     * @param user The authenticated user making the change
     * @param rarId The ID of the dataset RAR configuration to update
     * @param newTemplateId The ID of the new template to associate with
     * @return The updated configuration with new template association and recalculated values
     * @throws EmoticsException if configuration/template not found or user lacks access
     * </pre>
     */
    @Override
    public DatasetRevenueAtRisk updateDatasetLinkedTemplate(User user, Integer datasetId, Integer rarId, Integer newTemplateId) {
        log.info("Updating dataset revenue at risk with new template: datasetId={}, rarId={}, newTemplateId={}", datasetId, rarId, newTemplateId);
        Dataset dataset = getDatasetById(datasetId);
        DatasetRevenueAtRisk datasetRevenueAtRisk = validateAndGetDatasetRevenueAtRisk(user, rarId);
        RevenueAtRiskTemplate newTemplate = validateAndGetTemplate(user, newTemplateId);
        //verify if datasetRevenueAtRisk is same workspace
        if (!Objects.equals(dataset.getWorkspaceId(), newTemplate.getWorkspaceId())) {
            throw new EmoticsException("Cannot update dataset revenue at risk with template from different workspace");
        }
        // BUSINESS RULE: Update template association and recalculate with new template parameters
        datasetRevenueAtRisk.setTemplateId(newTemplateId);
        updateTimestamps(datasetRevenueAtRisk, false);

        // Convert template info to revenue-at-risk info and recalculate
        RevenueAtRisk newRevenueAtRisk = RevenueAtRiskMapStruct.toRevenueAtRisk(newTemplate.getRevenueAtRisk());
        datasetRevenueAtRisk.setRevenueAtRisk(newRevenueAtRisk);
        recalculateIfNeeded(datasetRevenueAtRisk, true);

        datasetRevenueAtRiskMapper.updateDatasetRevenueAtRisk(datasetRevenueAtRisk);
        return datasetRevenueAtRisk;
    }

    @Override
    public DatasetRevenueAtRisk applyTemplateForDatasetSelectedRevenueAtRisk(User user, Integer datasetId, Integer newTemplateId) {
        DatasetRevenueAtRisk datasetRevenueAtRisk = datasetRevenueAtRiskMapper.getDatasetSelectedRevenueAtRisk(datasetId);
        RevenueAtRiskTemplate template = validateAndGetTemplate(user, newTemplateId);
        if (datasetRevenueAtRisk == null || datasetRevenueAtRisk.getTemplateId() == null) {
            DatasetRevenueAtRisk newDatasetRevenueAtRisk = DatasetRevenueAtRisk.builder()
                    .datasetId(datasetId)
                    .templateId(newTemplateId)
                    .revenueAtRisk(RevenueAtRiskMapStruct.toRevenueAtRisk(template.getRevenueAtRisk()))
                    .build();
           datasetRevenueAtRisk = this.createDatasetRevenueAtRisk(user, newDatasetRevenueAtRisk);
           datasetMapper.updateSelectedRarId(datasetId, datasetRevenueAtRisk.getId());
        } else {
            // Update existing dataset revenue at risk with the new template
            datasetRevenueAtRisk = this.updateDatasetLinkedTemplate(user, datasetId, datasetRevenueAtRisk.getId(), newTemplateId);
        }
        return datasetRevenueAtRisk;
    }

    /**
     * <pre>
     * Updates the revenue-at-risk data for a dataset configuration (non-template linked).
     *
     * Business Logic:
     * - Updates RAR parameters and values for configurations not linked to templates
     * - Allows full customization of risk parameters independent of workspace templates
     * - Automatically recalculates risk amounts based on new parameters
     * - This is for standalone/custom configurations that don't follow template patterns
     *
     * @param user The authenticated user making the change
     * @param datasetRevenueAtRisk The updated configuration data
     * @return The updated configuration with recalculated values
     * @throws EmoticsException if configuration is template-linked or user lacks access
     * </pre>
     */
    @Override
    public DatasetRevenueAtRisk updateDatasetRevenueAtRiskData(User user, DatasetRevenueAtRisk datasetRevenueAtRisk) {
        validateUserAccess(user, datasetRevenueAtRisk.getDatasetId());

        // Get existing configuration to check template linkage
        DatasetRevenueAtRisk existingConfig = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(datasetRevenueAtRisk.getId());
        if (existingConfig == null) {
            throw new EmoticsException("Dataset revenue at risk configuration not found");
        }

        // BUSINESS RULE: Only allow data updates for non-template-linked configurations
        if (existingConfig.getTemplateId() != null) {
            throw new EmoticsException("Cannot update data for template-linked configurations. Use template association update instead.");
        }

        updateTimestamps(datasetRevenueAtRisk, false);

        // BUSINESS RULE: Recalculate with new parameters
        recalculateIfNeeded(datasetRevenueAtRisk, true);

        datasetRevenueAtRiskMapper.updateDatasetRevenueAtRisk(datasetRevenueAtRisk);
        return datasetRevenueAtRisk;
    }

    /**
     * <pre>
     * Enhanced preview method with smart detection for custom data or template-based previews.
     *
     * Business Logic:
     * This method supports two distinct preview scenarios:
     *
     * 1. CUSTOM DATA PREVIEW:
     *    - User provides specific RAR parameters to test
     *    - Useful for experimenting with different risk weights, customer counts, etc.
     *    - No template dependency - pure parameter-based calculation
     *
     * 2. TEMPLATE PREVIEW:
     *    - User selects a workspace template to preview its impact
     *    - Useful for comparing different template configurations
     *    - Template parameters are loaded and applied to current dataset
     *
     * 3. FALLBACK PREVIEW:
     *    - If neither custom data nor template is provided, uses existing configuration
     *    - Useful for re-calculating existing configurations with current dataset content
     *
     * @param user The authenticated user requesting the preview
     * @param datasetId The ID of the dataset to preview against
     * @param rarId The ID of existing RAR configuration (optional, can be null)
     * @param request The preview request containing either custom data or template ID
     * @return Preview result with calculated values (not persisted)
     * @throws EmoticsException if user lacks access or data is invalid
     * </pre>
     */
    @Override
    public DatasetRevenueAtRisk previewDatasetRevenueAtRisk(User user, Integer datasetId, Integer rarId, DatasetRevenueAtRiskPreviewRequest request) {
        validateUserAccess(user, datasetId);

        DatasetRevenueAtRisk existingConfig = getExistingConfigIfProvided(user, rarId);
        RevenueAtRisk previewData = getPreviewData(user, request, existingConfig);

        return calculatePreviewResult(datasetId, rarId, previewData, existingConfig);
    }

    /**
     * <pre>
     * Deletes a revenue-at-risk configuration from a dataset.
     *
     * @param user The authenticated user requesting deletion
     * @param id The ID of the RAR configuration to delete
     * @throws EmoticsException if configuration not found or user lacks access
     * </pre>
     */
    @Override
    public void deleteDatasetRevenueAtRisk(User user, Integer id) {
        DatasetRevenueAtRisk datasetRevenueAtRisk = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(id);
        if (datasetRevenueAtRisk == null) {
            throw new EmoticsException("Dataset revenue at risk not found");
        }
        validateUserAccess(user, datasetRevenueAtRisk.getDatasetId());
        datasetRevenueAtRiskMapper.deleteDatasetRevenueAtRisk(id);
    }

    /**
     * Retrieves the ID of the currently selected revenue-at-risk configuration for a dataset.
     *
     * @param user The authenticated user requesting the information
     * @param datasetId The ID of the dataset
     * @return The ID of the selected RAR configuration, or null if none selected
     * @throws EmoticsException if user lacks access to the dataset
     */
    @Override
    public Integer getSelectedRevenueAtRiskIdForDataset(User user, Integer datasetId) {
        validateUserAccess(user, datasetId);
        Dataset dataset = getDatasetById(datasetId);
        return dataset != null ? dataset.getSelectedRarId() : null;
    }

    // Helper methods

    /**
     * Resolves the DatasetRevenueAtRisk by either creating a new default or fetching existing.
     * If rarId is null, create a new one, with template is default template from workspace. This is backward compatible with existing code.
     */
    private DatasetRevenueAtRisk getOrCreateDatasetRevenueAtRisk(Integer datasetId, Integer rarId) {
        if (rarId == null) {
            DatasetRevenueAtRisk result = createDefaultDatasetRevenueAtRisk(datasetId);
            if (result == null) {
                throw new EmoticsException("Failed to create default dataset revenue at risk for dataset: " + datasetId);
            }
            return result;
        } else {
            DatasetRevenueAtRisk result = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(rarId);
            if (result == null) {
                throw new EmoticsException("Dataset revenue at risk not found with ID: " + rarId);
            }
            return result;
        }
    }


    /**
     * Applies the linked template data to the DatasetRevenueAtRisk.
     * If templateId is null, no changes are made (custom calculation).
     * If templateId is valid, it updates the name and revenueAtRisk fields.
     */
    private void applyTemplateData(User user, DatasetRevenueAtRisk datasetRevenueAtRisk) {
        Integer templateId = datasetRevenueAtRisk.getTemplateId();
        if (templateId == null) {
            // Note: If templateId is null (custom calculation), leave the name as is (no changes)
            return;
        }
        RevenueAtRiskTemplate linkedTemplate = revenueAtRiskTemplateService.getTemplateById(user, templateId);
        if (linkedTemplate != null) {
            datasetRevenueAtRisk.setName(linkedTemplate.getName());
            RevenueAtRisk revenueAtRisk = RevenueAtRiskMapStruct.toRevenueAtRisk(linkedTemplate.getRevenueAtRisk());
            datasetRevenueAtRisk.setRevenueAtRisk(revenueAtRisk);
        }
    }

    /**
     * Ensures revenue at risk amounts are calculated and valid.
     * BUSINESS RULE: Auto-recalculation for stale or missing risk amounts
     */
    private void validateAndRecalculate(User user, DatasetRevenueAtRisk datasetRevenueAtRisk) {
        // Check if we need to recalculate the revenue at risk amount
        RevenueAtRisk revenueAtRisk = datasetRevenueAtRisk.getRevenueAtRisk();
        if (revenueAtRisk != null) {
            updateDatasetRevenueAtRisk(user, datasetRevenueAtRisk, true);
        }
    }

    /**
     * Determines if revenue at risk calculation is needed.
     */
    private boolean checkIfRecalculation(RevenueAtRisk revenueAtRisk) {
        BigDecimal amount = revenueAtRisk.getRevenueAtRiskAmount();
        return amount == null || amount.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * Validates and retrieves DatasetRevenueAtRisk by ID.
     */
    private DatasetRevenueAtRisk validateAndGetDatasetRevenueAtRisk(User user, Integer rarId) {
        DatasetRevenueAtRisk datasetRevenueAtRisk = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(rarId);
        if (datasetRevenueAtRisk == null) {
            throw new EmoticsException("Dataset revenue at risk configuration not found");
        }
        validateUserAccess(user, datasetRevenueAtRisk.getDatasetId());
        return datasetRevenueAtRisk;
    }

    /**
     * Validates and retrieves RevenueAtRiskTemplate by ID.
     */
    private RevenueAtRiskTemplate validateAndGetTemplate(User user, Integer templateId) {
        RevenueAtRiskTemplate template = revenueAtRiskTemplateService.getTemplateById(user, templateId);
        if (template == null) {
            throw new EmoticsException("Template not found or access denied");
        }
        return template;
    }

    /**
     * Updates timestamps for creation or update operations.
     */
    private void updateTimestamps(DatasetRevenueAtRisk datasetRevenueAtRisk, boolean isCreation) {
        LocalDateTime now = LocalDateTime.now();
        if (isCreation) {
            datasetRevenueAtRisk.setCreatedAt(now);
        }
        datasetRevenueAtRisk.setUpdatedAt(now);
    }

    /**
     * Recalculates revenue at risk if needed and data is present.
     */
    private void recalculateIfNeeded(DatasetRevenueAtRisk datasetRevenueAtRisk, boolean shouldRecalculate) {
        if (shouldRecalculate && datasetRevenueAtRisk.getRevenueAtRisk() != null) {
            RevenueAtRisk calculatedRar = revenueAtRiskService.recalculateRarInfo(
                    datasetRevenueAtRisk.getDatasetId(),
                    datasetRevenueAtRisk.getRevenueAtRisk()
            );
            datasetRevenueAtRisk.setRevenueAtRisk(calculatedRar);
        }
    }

    /**
     * Applies updates from request to the entity.
     */
    private void applyUpdateFromRequest(DatasetRevenueAtRisk datasetRevenueAtRisk,
                                        DatasetRevenueAtRiskRequest request,
                                        User user) {
        // Map non-nullable fields from request to entity
        if (request.getName() != null && !request.getName().isEmpty()) {
            datasetRevenueAtRisk.setName(request.getName());
        }
        if (request.getRevenueAtRisk() != null) {
            datasetRevenueAtRisk.setRevenueAtRisk(request.getRevenueAtRisk());
        }
        if (request.getTemplateId() != null) {
            // Validate template ID if provided
            validateAndGetTemplate(user, request.getTemplateId());
            datasetRevenueAtRisk.setTemplateId(request.getTemplateId());
        }
    }

    /**
     * Gets existing configuration if rarId is provided and validates access.
     */
    private DatasetRevenueAtRisk getExistingConfigIfProvided(User user, Integer rarId) {
        if (rarId == null) return null;

        DatasetRevenueAtRisk existingConfig = datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(rarId);
        if (existingConfig != null) {
            validateUserAccess(user, existingConfig.getDatasetId());
        }
        return existingConfig;
    }

    /**
     * Resolves preview data from request or existing configuration.
     * BUSINESS LOGIC: Smart detection of preview source
     */
    private RevenueAtRisk getPreviewData(User user, DatasetRevenueAtRiskPreviewRequest request, DatasetRevenueAtRisk existingConfig) {
        if (request.getTemplateId() != null) {
            // Scenario 1: Preview with workspace template
            RevenueAtRiskTemplate template = validateAndGetTemplate(user, request.getTemplateId());
            return RevenueAtRiskMapStruct.toRevenueAtRisk(template.getRevenueAtRisk());
        } else if (request.getRevenueAtRisk() != null) {
            // Scenario 2: Preview with custom RAR data
            return request.getRevenueAtRisk();
        } else if (existingConfig != null && existingConfig.getRevenueAtRisk() != null) {
            // Scenario 3: Fallback to existing configuration data
            return existingConfig.getRevenueAtRisk();
        } else {
            throw new EmoticsException("No preview data provided. Please specify either customRevenueAtRisk or previewTemplateId.");
        }
    }

    /**
     * Calculates and creates preview result.
     */
    private DatasetRevenueAtRisk calculatePreviewResult(Integer datasetId, Integer rarId,
                                                        RevenueAtRisk previewData,
                                                        DatasetRevenueAtRisk existingConfig) {
        // Calculate preview values without persisting
        RevenueAtRisk calculatedPreview = revenueAtRiskService.previewRarInfo(datasetId, previewData);

        // Create and return preview result
        DatasetRevenueAtRisk previewResult = new DatasetRevenueAtRisk();
        previewResult.setId(rarId);
        previewResult.setDatasetId(datasetId);
        previewResult.setRevenueAtRisk(calculatedPreview);

        // Copy metadata from existing config if available
        if (existingConfig != null) {
            previewResult.setName(existingConfig.getName());
            previewResult.setTemplateId(existingConfig.getTemplateId());
            previewResult.setCreatedBy(existingConfig.getCreatedBy());
            previewResult.setCreatedAt(existingConfig.getCreatedAt());
        }

        return previewResult;
    }

    /**
     * Helper method to retrieve a dataset by its ID.
     *
     * @param datasetId The ID of the dataset to retrieve
     * @return The dataset entity, or null if not found
     */
    private Dataset getDatasetById(Integer datasetId) {
        return datasetMapper.selectById(datasetId);
    }

    private DatasetRevenueAtRisk createDefaultDatasetRevenueAtRisk(Integer datasetId) {
        Dataset dataset = getDatasetById(datasetId);
        Integer workspaceId = dataset.getWorkspaceId();
        applyDatasetRevenueAtRiskService.initialDatasetRevenueAtRisk(workspaceId, datasetId);
        // After copying, we can retrieve the newly created RAR ID
        return datasetRevenueAtRiskMapper.getDatasetSelectedRevenueAtRisk(datasetId);
    }

    /**
     * Validates that the user has access to the specified dataset.
     * This method checks if the dataset exists and if the user has appropriate permissions.
     *
     * @param user The user requesting access
     * @param datasetId The ID of the dataset to validate access for
     * @throws EmoticsException if the dataset is not found or user doesn't have access
     */
    private void validateUserAccess(User user, Integer datasetId) {
        if (datasetId == null) {
            throw new EmoticsException("Dataset ID cannot be null");
        }

        Dataset dataset = getDatasetById(datasetId);
        if (dataset == null) {
            throw new EmoticsException("Dataset not found");
        }

        // Basic validation - check if user owns the dataset or has access through workspace
        // You may need to add more sophisticated permission checking based on your business logic
        if (!Objects.equals(dataset.getUserId(), user.getId()) &&
                (user.getWorkspaceIds() == null || !user.getWorkspaceIds().contains(dataset.getWorkspaceId()))) {
            throw new EmoticsException("Access denied to dataset");
        }
    }

    private DatasetRevenueAtRisk initCustomDatasetRevenueAtRisk(User user, Integer datasetId) {
        return DatasetRevenueAtRisk.builder()
                .datasetId(datasetId)
                .revenueAtRisk( new RevenueAtRisk(RevenueAtRiskType.CUSTOMER, RevenueAtRiskWeight.MEDIUM))
                .createdBy(user.getId())
                .name("Dataset Custom Calculation")
                .build();
    }
}