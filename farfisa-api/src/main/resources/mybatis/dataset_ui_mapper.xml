<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~  Copyright 2022 Adoreboard Ltd. All rights reserved.
  ~  ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
  -->

<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adoreboard.emotics.api.v0.dataset.mapper.DatasetUIMapper">

    <!--            -->
    <!--  DATASETS  -->
    <!--            -->

    <select id="countDatasets" resultType="int" parameterType="com.adoreboard.emotics.api.v0.dataset.mapper.DatasetUIParam">
        WITH
             dataset_tags AS (<include refid="selectDatasetTagIds"/>)
        SELECT COUNT(0)
        FROM bulk_upload d
            LEFT JOIN dataset_tags ON dataset_tags.dataset_id = d.id
        <where>
            <if test="userId != null and workspaceId == null">AND d.user_id = #{userId}</if>
            <if test="workspaceId != null">
                AND d.workspace_id = #{workspaceId}
                <if test="datasetIds != null and userId != null">
                    AND (
                        d.user_id = #{userId}
                        <if test="datasetIds != null and datasetIds.size() >0">
                            OR d.id IN <foreach item="item" collection="datasetIds" open="(" separator="," close=")">#{item}</foreach>
                        </if>
                    )
                </if>
            </if>
            <if test="term != null">AND d.label ILIKE '%${term}%'</if>
            <if test="ignoreStatuses != null and ignoreStatuses.size &gt; 0">
                AND d.status NOT IN
                <foreach item="ignStatus" index="i" collection="ignoreStatuses" open="(" separator="," close=")">#{ignStatus}</foreach>
            </if>
            AND NOT d.archived
            <if test="tagIds != null">
              AND #{tagIds,typeHandler=com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler} &amp;&amp; dataset_tags.tag_ids
            </if>
            AND NOT(d.status = 'finished' AND d.summary IS NULL)
        </where>
    </select>

    <select id="countArchived" resultType="int" parameterType="com.adoreboard.emotics.api.v0.dataset.mapper.DatasetUIParam">
        WITH
             dataset_tags AS (<include refid="selectDatasetTagIds"/>)
        SELECT COUNT(0)
        FROM bulk_upload d
            LEFT JOIN dataset_tags ON dataset_tags.dataset_id = d.id
        <where>
            <if test="userId != null and workspaceId == null">AND d.user_id = #{userId}</if>
            <if test="workspaceId != null">
                AND d.workspace_id = #{workspaceId}
                <if test="datasetIds != null and userId != null">
                    AND (
                        d.user_id = #{userId}
                        <if test="datasetIds != null and datasetIds.size() >0">
                            OR d.id IN <foreach item="item" collection="datasetIds" open="(" separator="," close=")">#{item}</foreach>
                        </if>
                    )
                </if>
            </if>
            <if test="term != null">AND d.label ILIKE '%${term}%'</if>
            AND d.archived
            <if test="tagIds != null">
              AND #{tagIds,typeHandler=com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler} &amp;&amp; dataset_tags.tag_ids
            </if>
        </where>
    </select>

    <select id="countInProgress" resultType="int" parameterType="com.adoreboard.emotics.api.v0.dataset.mapper.DatasetUIParam">
        WITH
            dataset_tags AS (<include refid="selectDatasetTagIds"/>)
        SELECT COUNT(0)
        FROM bulk_upload d
            LEFT JOIN dataset_tags ON dataset_tags.dataset_id = d.id
        <where>
            <if test="userId != null and workspaceId == null">AND d.user_id = #{userId}</if>
            <if test="workspaceId != null">
                AND d.workspace_id = #{workspaceId}
                <if test="datasetIds != null and userId != null">
                    AND (
                        d.user_id = #{userId}
                        <if test="datasetIds != null and datasetIds.size() >0">
                            OR d.id IN <foreach item="item" collection="datasetIds" open="(" separator="," close=")">#{item}</foreach>
                        </if>
                    )
                </if>
            </if>
            <if test="term != null">AND d.label ILIKE '%${term}%'</if>
            <if test="ignoreStatuses != null and ignoreStatuses.size &gt; 0">
                AND d.status NOT IN
                <foreach item="ignStatus" index="i" collection="ignoreStatuses" open="(" separator="," close=")">#{ignStatus}</foreach>
            </if>
            AND NOT d.archived
            <if test="tagIds != null">
              AND #{tagIds,typeHandler=com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler} &amp;&amp; dataset_tags.tag_ids
            </if>
        </where>
    </select>

    <select id="getRelativeCounts" parameterType="map" resultMap="relativeCountMap">
        SELECT
            p.id,
            COALESCE(COUNT(DISTINCT c.id), 0) as child_dataset_count,
            COALESCE(COUNT(DISTINCT f.filter_list_id), 0) as filter_view_count
        FROM bulk_upload p
            LEFT JOIN bulk_upload c on c.parent_id = p.id
            LEFT JOIN metadata_filters f on f.parent_id = p.id
        WHERE p.id IN <foreach item="id" index="i" collection="ids" open="(" separator="," close=")">#{id}</foreach>
        <if test="parentOnly">
            AND f.filter_list_parent_id IS NULL
        </if>
        GROUP BY p.id
    </select>

    <select id="selectDatasets" resultMap="datasetMap"
            parameterType="com.adoreboard.emotics.api.v0.dataset.mapper.DatasetUIParam">
        WITH datasets AS (
        SELECT *
        FROM bulk_upload d
        <where>
            <if test="userId != null and workspaceId == null">AND d.user_id = #{userId}</if>
            <if test="workspaceId != null">
                AND d.workspace_id = #{workspaceId}
                <if test="datasetIds != null and userId != null">
                    AND (
                    d.user_id = #{userId}
                    <if test="datasetIds != null and datasetIds.size() >0">
                        OR d.id IN
                        <foreach item="item" collection="datasetIds" open="(" separator="," close=")">#{item}</foreach>
                    </if>
                    )
                </if>
            </if>
            <if test="term != null">AND d.label ILIKE '%${term}%'</if>
            <if test="archived != null and archived">AND d.archived</if>
            <if test="archived == null or !archived">AND NOT d.archived</if>
            <if test="selected != null">
                AND d.id IN
                <foreach item="item" index="i" collection="selected" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="inProgress != null and inProgress">AND d.status != 'finished'</if>
            <if test="inProgress != null and !inProgress">AND d.status = 'finished'</if>
            <if test="ignoreStatuses != null and ignoreStatuses.size &gt; 0">
                AND d.status NOT IN
                <foreach item="ignStatus" index="i" collection="ignoreStatuses" open="(" separator="," close=")">#{ignStatus}</foreach>
            </if>
            AND NOT(d.status = 'finished' AND d.summary IS NULL)
        </where>
        ),
        <include refid="datasetCommon.withStatusesAsBupsm"/>,
        <include refid="datasetCommon.withStopwordsAsBusw"/>,
        <include refid="datasetCommon.withDownloadDetailsAsDdd"/>,
        <include refid="datasetCommon.withTagsAsDt"/>,
        <include refid="datasetCommon.withStorytellerReport"/>,
        <include refid="datasetCommon.withRevenueAtRisk"/>,
        dp AS (
        SELECT *
        FROM dataset_permission_view
        WHERE dataset_id IN (SELECT id FROM datasets)
        )
        SELECT
        d.id,
        d.user_id,
        d.workspace_id,
        d.label,
        (d.summary ->> 'polarity') :: DOUBLE PRECISION AS polarity,
        (d .summary ->> 'adoreScore') :: NUMERIC AS adorescore,
        d.document_count,
        d.summary,
        d.status,
        d.upload_start,
        ddd.analysis_download_location AS analysis_download_location,
        ddd.original_download_location AS original_download_location,
        d.archived,
        d.pending_changes,
        d.execution_id,
        d.exclude_auto_topics,
        d.features,
        d.metadata_headers,
        d.metadata_columns,
        d.metadata_types,
        bupsm.progress_status_map,
        ((bupsm.progress_status_map->'finished'->>'updatedAt') :: TIMESTAMP ) as finishedAt,
        busw.stop_words,
        dt.tag_ids,
        dp.permission_type,
        dp.editor_ids,
        dp.viewer_ids,
        dp.group_id,
        rp.report_count,
        rar.*
        FROM datasets d
        LEFT OUTER JOIN bupsm ON bupsm.dataset_id = d.id
        LEFT OUTER JOIN busw ON busw.dataset_id = d.id
        LEFT OUTER JOIN ddd ON ddd.dataset_id = d.id
        LEFT OUTER JOIN dt ON dt.dataset_id = d.id
        LEFT OUTER JOIN dp ON dp.dataset_id = d.id
        LEFT OUTER JOIN rp ON rp.dataset_id = d.id
        LEFT OUTER JOIN rar ON rar.rar_dataset_id = d.id
        <where>
            <if test="tagIds != null">
                AND #{tagIds,typeHandler=com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler} &amp;&amp;
                dt.tag_ids
            </if>
        </where>
        <if test="orderBy == null">ORDER BY d.id DESC</if>
        <if test="orderBy != null and orderBy != 'permission'">ORDER BY ${orderBy}</if>
        <if test="orderBy != null and orderBy != 'permission' and order != null">${order}</if>
        <if test="orderBy != null and orderBy == 'permission'">
            ORDER BY permission_order
            <if test="order == 'ASC'">ASC NULLS FIRST</if>
            <if test="order == 'DESC'">DESC NULLS LAST</if>,
            total_users <if test="order != null">${order}</if>,
            group_label
            <if test="order != null">${order}</if>
        </if>
        <if test="offset != null and offset >= 0">OFFSET #{offset}</if>
        <if test="limit != null and limit > 0">LIMIT #{limit}</if>
    </select>

    <sql id="selectDatasetTagIds">
        SELECT dt.dataset_id,
               array_agg(dt.tag_id) as tag_ids
        FROM bulk_upload_dataset_tag dt
        GROUP BY dt.dataset_id
    </sql>

    <resultMap id="datasetMap" type="com.adoreboard.emotics.api.v0.dataset.model.DatasetUIModel" autoMapping="true">
        <id column="id" property="id"/>
        <result column="summary" property="summary" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToPreAnalysisSummary"/>
        <result column="progress_status_map" property="statusProgresses" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToStatusProgresses"/>
        <result column="stop_words" property="stopWords" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToStopWords"/>
        <result column="features" property="features" typeHandler="com.adoreboard.emotics.common.mybatis.ListDatasetFeatureEnumTypeHandler"/>
        <result column="metadata_headers" property="metadataHeaders" typeHandler="com.adoreboard.emotics.common.mybatis.StringListTypeHandler"/>
        <result column="metadata_columns" property="metadataColumns" typeHandler="com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler"/>
        <result column="metadata_types" property="metadataTypes" typeHandler="com.adoreboard.emotics.common.mybatis.StringListTypeHandler"/>
        <result column="tag_ids" property="tagIds" typeHandler="com.adoreboard.emotics.common.mybatis.IntegerListTypeHandler"/>
        <result column="report_count" property="reportCount"/>
        <association property="datasetRevenueAtRisk" javaType="com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRiskWithUser">
            <id column="rar_id" property="id"/>
            <result column="rar_dataset_id" property="datasetId"/>
            <result column="rar_name" property="name"/>
            <result column="rar_template_id" property="templateId"/>
            <result column="rar_created_by" property="createdBy"/>
            <result column="rar_revenue_at_risk" property="revenueAtRisk" typeHandler="com.adoreboard.emotics.common.mybatis.MyBatisMapJsonbToRevenueAtRisk"/>
            <result column="rar_created_at" property="createdAt"/>
            <result column="rar_updated_at" property="updatedAt"/>
            <association property="user" javaType="com.adoreboard.emotics.common.model.User">
                <result column="u_id" property="id"/>
                <result column="u_loginName" property="loginName"/>
                <result column="u_firstName" property="firstName"/>
                <result column="u_lastName" property="lastName"/>
            </association>
        </association>
    </resultMap>

    <resultMap id="relativeCountMap" type="com.adoreboard.emotics.api.v0.dataset.model.DatasetUIRelativeCount" autoMapping="true">
        <result column="id" property="id" />
        <result column="child_dataset_count" property="childDatasetCount" />
        <result column="filter_view_count" property="filterViewCount" />
    </resultMap>
</mapper>