package com.adoreboard.emotics.api.dataset.service.impl;

import com.adoreboard.emotics.api.dataset.model.DatasetRevenueAtRiskRequest;
import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.RevenueAtRiskTemplateService;
import com.adoreboard.emotics.api.revenueAtRisk.service.RevenueAtRiskService;
import com.adoreboard.emotics.common.exception.EmoticsException;
import com.adoreboard.emotics.common.mapper.DatasetMapper;
import com.adoreboard.emotics.common.mapper.DatasetRevenueAtRiskMapper;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplateInfo;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRisk;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class DatasetRevenueAtRiskServiceImplTest {
    
    @Mock
    private DatasetRevenueAtRiskMapper datasetRevenueAtRiskMapper;
    
    @Mock
    private DatasetMapper datasetMapper;
    
    @Mock
    private RevenueAtRiskTemplateService revenueAtRiskTemplateService;
    
    @Mock
    private RevenueAtRiskService revenueAtRiskService;
    
    @InjectMocks
    private DatasetRevenueAtRiskServiceImpl datasetRevenueAtRiskService;
    
    private User testUser;
    private DatasetRevenueAtRisk testDatasetRevenueAtRisk;
    private RevenueAtRisk testValueAtRiskInfo;
    private Dataset testDataset;
    
    @Before
    public void setUp() {
        testUser = new User();
        testUser.setId(98);
        // Set up workspace access for the user to pass validation
        testUser.setWorkspaceIds(Set.of(100, 200, 300)); // Include workspace 100

        testValueAtRiskInfo = new RevenueAtRisk();
        testValueAtRiskInfo.setNumberOfCustomers(100);

        testDatasetRevenueAtRisk = DatasetRevenueAtRisk.builder()
                .id(1)
                .datasetId(123)
                .templateId(456)
                .revenueAtRisk(testValueAtRiskInfo)
                .build();

        testDataset = new Dataset();
        testDataset.setId(123);
        testDataset.setUserId(98); // Set the dataset owner to match testUser.getId()
        testDataset.setWorkspaceId(100); // Set workspace ID that user has access to
        testDataset.setSelectedRarId(789);
    }
    
    @Test
    public void shouldGetDatasetRevenueAtRisks() {
        // Given
        Integer datasetId = 123;
        List<DatasetRevenueAtRisk> expectedTemplates = Collections.singletonList(testDatasetRevenueAtRisk);

        // Mock dataset access validation
        given(datasetMapper.selectById(datasetId)).willReturn(testDataset);
        given(datasetRevenueAtRiskMapper.getDatasetRevenueAtRisks(datasetId)).willReturn(expectedTemplates);

        // When
        List<DatasetRevenueAtRisk> result = datasetRevenueAtRiskService.getDatasetRevenueAtRisks(testUser, datasetId);

        // Then
        assertEquals(expectedTemplates, result);
        verify(datasetMapper).selectById(datasetId);
        verify(datasetRevenueAtRiskMapper).getDatasetRevenueAtRisks(datasetId);
    }

    @Test
    public void shouldGetDatasetRevenueAtRisk() {
        // Given
        Integer id = 1;

        // Mock dataset access validation
        given(datasetMapper.selectById(testDatasetRevenueAtRisk.getDatasetId())).willReturn(testDataset);
        given(datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(id)).willReturn(testDatasetRevenueAtRisk);

        // When
        DatasetRevenueAtRisk result = datasetRevenueAtRiskService.getDatasetRevenueAtRisk(testUser, testDataset.getId(), id);

        // Then
        assertEquals(testDatasetRevenueAtRisk, result);
        verify(datasetMapper).selectById(testDatasetRevenueAtRisk.getDatasetId());
        verify(datasetRevenueAtRiskMapper).getDatasetRevenueAtRisk(id);
    }

    @Test
    public void shouldCreateDatasetRevenueAtRisk() {
        // Given
        DatasetRevenueAtRisk newTemplate = DatasetRevenueAtRisk.builder()
                .datasetId(123)
                .templateId(456)
                .revenueAtRisk(testValueAtRiskInfo)
                .build();

        RevenueAtRisk calculatedRar = new RevenueAtRisk();
        calculatedRar.setNumberOfCustomers(150);

        // Mock dataset access validation
        given(datasetMapper.selectById(123)).willReturn(testDataset);
        given(revenueAtRiskService.recalculateRarInfo(eq(123), any(RevenueAtRisk.class)))
                .willReturn(calculatedRar);

        // When
        DatasetRevenueAtRisk result = datasetRevenueAtRiskService.createDatasetRevenueAtRisk(testUser, newTemplate);

        // Then
        assertNotNull(result.getCreatedAt());
        assertNotNull(result.getUpdatedAt());
        assertEquals(testUser.getId(), result.getCreatedBy());
        assertEquals(calculatedRar, result.getRevenueAtRisk());

        verify(datasetMapper).selectById(123);
        verify(revenueAtRiskService).recalculateRarInfo(123, testValueAtRiskInfo);
        verify(datasetRevenueAtRiskMapper).createDatasetRevenueAtRisk(newTemplate);
    }

    @Test
    public void shouldCreateDatasetRevenueAtRiskWithoutRevenueAtRisk() {
        // Given
        DatasetRevenueAtRisk newTemplate = DatasetRevenueAtRisk.builder()
                .datasetId(123)
                .templateId(456)
                .revenueAtRisk(null)
                .build();

        // Mock dataset access validation
        given(datasetMapper.selectById(123)).willReturn(testDataset);

        // When
        DatasetRevenueAtRisk result = datasetRevenueAtRiskService.createDatasetRevenueAtRisk(testUser, newTemplate);

        // Then
        assertNotNull(result.getCreatedAt());
        assertNotNull(result.getUpdatedAt());
        assertEquals(testUser.getId(), result.getCreatedBy()); // Convert Integer to int
        assertNull(result.getRevenueAtRisk());

        verify(datasetMapper).selectById(123);
        //verify(valueAtRiskService, never()).recalculateVarInfo(any(), any());
        verify(datasetRevenueAtRiskMapper).createDatasetRevenueAtRisk(newTemplate);
    }

    @Test
    public void shouldUpdateDatasetRevenueAtRisk() {
        // Given
        RevenueAtRisk calculatedRar = new RevenueAtRisk();
        calculatedRar.setNumberOfCustomers(200);
        DatasetRevenueAtRiskRequest updateRequest = new DatasetRevenueAtRiskRequest();
        updateRequest.setTemplateId(testDatasetRevenueAtRisk.getTemplateId());
        updateRequest.setRevenueAtRisk(testValueAtRiskInfo);

        given(datasetMapper.selectById(123)).willReturn(testDataset);
        given(datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(testDataset.getSelectedRarId())).willReturn(testDatasetRevenueAtRisk);
        given(revenueAtRiskTemplateService.getTemplateById(testUser, 456))
                .willReturn(RevenueAtRiskTemplate.builder().id(456).revenueAtRisk(new RevenueAtRiskTemplateInfo()).build());

        when(revenueAtRiskService.recalculateRarInfo(eq(123), any(RevenueAtRisk.class)))
                .thenReturn(calculatedRar);

        // When
        DatasetRevenueAtRisk result = datasetRevenueAtRiskService.updateDatasetRevenueAtRisk(testUser, testDataset.getId(), updateRequest);

        // Then
        verify(revenueAtRiskService, never()).recalculateRarInfo(123, testValueAtRiskInfo);
        verify(datasetRevenueAtRiskMapper).updateDatasetRevenueAtRisk(testDatasetRevenueAtRisk);
    }

    @Test
    public void shouldDeleteDatasetRevenueAtRisk() {
        // Given
        // Mock dataset access validation
        given(datasetMapper.selectById(123)).willReturn(testDataset);
        given(datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(1)).willReturn(testDatasetRevenueAtRisk);
        // When
        datasetRevenueAtRiskService.deleteDatasetRevenueAtRisk(testUser, 1);
        // Then
        verify(datasetRevenueAtRiskMapper).deleteDatasetRevenueAtRisk(1);
    }

    @Test
    public void shouldGetSelectedRevenueAtRiskIdForDataset() {
        // Given
        int datasetId = 123;
        given(datasetMapper.selectById(datasetId)).willReturn(testDataset);
        // When
        Integer result = datasetRevenueAtRiskService.getSelectedRevenueAtRiskIdForDataset(testUser, datasetId);
        // Then
        assertEquals(testDataset.getSelectedRarId(), result);
    }

    @Test
    public void shouldHandleComplexCloneScenarioWithMultipleTemplates() {
        // Given
        Integer datasetId = 123;
        Integer workspaceId = 99;

        // Create multiple cloned templates
        DatasetRevenueAtRisk template1 = DatasetRevenueAtRisk.builder()
                .id(1).datasetId(datasetId).templateId(456).revenueAtRisk(testValueAtRiskInfo).build();
        DatasetRevenueAtRisk template2 = DatasetRevenueAtRisk.builder()
                .id(2).datasetId(datasetId).templateId(789).revenueAtRisk(testValueAtRiskInfo).build();
        DatasetRevenueAtRisk template3 = DatasetRevenueAtRisk.builder()
                .id(3).datasetId(datasetId).templateId(101).revenueAtRisk(testValueAtRiskInfo).build();

        List<DatasetRevenueAtRisk> clonedTemplates = Arrays.asList(template1, template2, template3);

        // Default template matches template2
        RevenueAtRiskTemplate defaultTemplate = RevenueAtRiskTemplate.builder()
                .id(789)
                .defaultTemplate(true)
                .build();

        RevenueAtRisk calculatedRar = new RevenueAtRisk();
        calculatedRar.setNumberOfCustomers(200);

        when(datasetRevenueAtRiskMapper.getDatasetRevenueAtRisks(datasetId)).thenReturn(clonedTemplates);
        when(revenueAtRiskTemplateService.getDefaultTemplate(testUser, workspaceId)).thenReturn(defaultTemplate);
        when(revenueAtRiskService.recalculateRarInfo(eq(datasetId), any(RevenueAtRisk.class)))
                .thenReturn(calculatedRar);
    }

    @Test
    public void shouldUpdateTemplateAssociation() {
        // Given
        Integer rarId = 1;
        Integer newTemplateId = 2;
        Integer datasetId = 123;

        DatasetRevenueAtRisk existingConfig = createTestDatasetRevenueAtRisk(rarId, datasetId, 1);
        RevenueAtRiskTemplate newTemplate = createTestTemplate(newTemplateId);
        RevenueAtRisk calculatedRar = new RevenueAtRisk();

        // Mock dataset access validation
        given(datasetMapper.selectById(datasetId)).willReturn(testDataset);
        given(datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(rarId)).willReturn(existingConfig);
        given(revenueAtRiskTemplateService.getTemplateById(testUser, newTemplateId)).willReturn(newTemplate);
        given(revenueAtRiskService.recalculateRarInfo(eq(datasetId), any(RevenueAtRisk.class))).willReturn(calculatedRar);

        // When
        DatasetRevenueAtRisk result = datasetRevenueAtRiskService.updateDatasetLinkedTemplate(testUser, testDataset.getId(), rarId, newTemplateId);

        // Then
        assertNotNull(result);
        assertEquals(newTemplateId, result.getTemplateId());
        assertEquals(calculatedRar, result.getRevenueAtRisk());
        verify(datasetRevenueAtRiskMapper).updateDatasetRevenueAtRisk(result);
    }

    @Test(expected = EmoticsException.class)
    public void shouldThrowExceptionWhenUpdatingNonExistentConfig() {
        // Given
        Integer rarId = 999;
        Integer newTemplateId = 2;

        given(datasetMapper.selectById(testDataset.getId())).willReturn(testDataset);
        given(datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(rarId)).willReturn(null);

        // When
        datasetRevenueAtRiskService.updateDatasetLinkedTemplate(testUser, testDataset.getId(), rarId, newTemplateId);

        // Then - exception should be thrown
    }

    @Test
    public void shouldUpdateRevenueAtRiskDataForNonTemplateLinkedConfig() {
        // Given
        Integer rarId = 1;
        Integer datasetId = 123;

        DatasetRevenueAtRisk existingConfig = createTestDatasetRevenueAtRisk(rarId, datasetId, null); // No template
        DatasetRevenueAtRisk updateRequest = createTestDatasetRevenueAtRisk(rarId, datasetId, null);
        RevenueAtRisk calculatedRar = new RevenueAtRisk();

        // Mock dataset access validation
        given(datasetMapper.selectById(datasetId)).willReturn(testDataset);
        given(datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(rarId)).willReturn(existingConfig);
        given(revenueAtRiskService.recalculateRarInfo(eq(datasetId), any(RevenueAtRisk.class))).willReturn(calculatedRar);

        // When
        DatasetRevenueAtRisk result = datasetRevenueAtRiskService.updateDatasetRevenueAtRiskData(testUser, updateRequest);

        // Then
        assertNotNull(result);
        assertEquals(calculatedRar, result.getRevenueAtRisk());
        verify(datasetMapper).selectById(datasetId);
        verify(datasetRevenueAtRiskMapper).updateDatasetRevenueAtRisk(result);
    }

    @Test(expected = EmoticsException.class)
    public void shouldThrowExceptionWhenUpdatingDataForTemplateLinkedConfig() {
        // Given
        Integer rarId = 1;
        Integer datasetId = 123;

        DatasetRevenueAtRisk existingConfig = createTestDatasetRevenueAtRisk(rarId, datasetId, 1); // Has template
        DatasetRevenueAtRisk updateRequest = createTestDatasetRevenueAtRisk(rarId, datasetId, 1);

        // Mock dataset access validation
        given(datasetMapper.selectById(datasetId)).willReturn(testDataset);
        given(datasetRevenueAtRiskMapper.getDatasetRevenueAtRisk(rarId)).willReturn(existingConfig);

        // When
        datasetRevenueAtRiskService.updateDatasetRevenueAtRiskData(testUser, updateRequest);

        // Then - exception should be thrown
    }

    @Test
    public void shouldValidateUserAccessWhenUserOwnsDataset() {
        // Given
        Integer datasetId = 123;
        Dataset dataset = new Dataset();
        dataset.setId(datasetId);
        dataset.setUserId(98); // Same as testUser.getId()
        dataset.setWorkspaceId(999); // Different workspace, but user owns dataset

        given(datasetMapper.selectById(datasetId)).willReturn(dataset);
        given(datasetRevenueAtRiskMapper.getDatasetRevenueAtRisks(datasetId)).willReturn(Collections.emptyList());

        // When & Then - Should not throw exception
        List<DatasetRevenueAtRisk> result = datasetRevenueAtRiskService.getDatasetRevenueAtRisks(testUser, datasetId);

        assertNotNull(result);
        verify(datasetMapper).selectById(datasetId);
    }

    @Test
    public void shouldValidateUserAccessWhenUserHasWorkspaceAccess() {
        // Given
        Integer datasetId = 123;
        Dataset dataset = new Dataset();
        dataset.setId(datasetId);
        dataset.setUserId(999); // Different user
        dataset.setWorkspaceId(100); // Workspace user has access to

        given(datasetMapper.selectById(datasetId)).willReturn(dataset);
        given(datasetRevenueAtRiskMapper.getDatasetRevenueAtRisks(datasetId)).willReturn(Collections.emptyList());

        // When & Then - Should not throw exception
        List<DatasetRevenueAtRisk> result = datasetRevenueAtRiskService.getDatasetRevenueAtRisks(testUser, datasetId);

        assertNotNull(result);
        verify(datasetMapper).selectById(datasetId);
    }

    @Test(expected = EmoticsException.class)
    public void shouldThrowExceptionWhenUserHasNoAccess() {
        // Given
        Integer datasetId = 123;
        Dataset dataset = new Dataset();
        dataset.setId(datasetId);
        dataset.setUserId(999); // Different user
        dataset.setWorkspaceId(999); // Workspace user doesn't have access to

        given(datasetMapper.selectById(datasetId)).willReturn(dataset);

        // When
        datasetRevenueAtRiskService.getDatasetRevenueAtRisks(testUser, datasetId);

        // Then - exception should be thrown
    }

    // Helper methods for new tests
    private DatasetRevenueAtRisk createTestDatasetRevenueAtRisk(Integer id, Integer datasetId, Integer templateId) {
        DatasetRevenueAtRisk config = new DatasetRevenueAtRisk();
        config.setId(id);
        config.setDatasetId(datasetId);
        config.setTemplateId(templateId);
        config.setRevenueAtRisk(new RevenueAtRisk());
        config.setRevenueAtRisk(new RevenueAtRisk());
        return config;
    }

    private RevenueAtRiskTemplate createTestTemplate(Integer templateId) {
        RevenueAtRiskTemplate template = new RevenueAtRiskTemplate();
        template.setId(templateId);
        template.setWorkspaceId(100);
        template.setRevenueAtRisk(new RevenueAtRiskTemplateInfo());
        return template;
    }
}